[![Build Status](https://travis-ci.org/sebas<PERSON><PERSON><PERSON>/php-timer.svg?branch=master)](https://travis-ci.org/sebastian<PERSON>mann/php-timer)

# phpunit/php-timer

Utility class for timing things, factored out of PHPUnit into a stand-alone component.

## Installation

You can add this library as a local, per-project dependency to your project using [Composer](https://getcomposer.org/):

    composer require phpunit/php-timer

If you only need this library during development, for instance to run your project's test suite, then you should add it as a development-time dependency:

    composer require --dev phpunit/php-timer

## Usage

### Basic Timing

```php
use <PERSON><PERSON><PERSON>gmann\Timer\Timer;

Timer::start();

// ...

$time = Timer::stop();
var_dump($time);

print Timer::secondsToTimeString($time);
```

The code above yields the output below:

    double(1.0967254638672E-5)
    0 ms

### Resource Consumption Since PHP Startup

```php
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Timer\Timer;

print Timer::resourceUsage();
```

The code above yields the output below:

    Time: 0 ms, Memory: 0.50MB

-- Criar tabela categorias
CREATE TABLE IF NOT EXISTS categorias (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NULL DEFAULT NULL
);

-- <PERSON>riar tabela promocoes
CREATE TABLE IF NOT EXISTS promocoes (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    clientes_id BIGINT UNSIGNED NOT NULL,
    categorias_id BIGINT UNSIGNED NOT NULL,
    titulo VARCHAR(255) NOT NULL,
    dtinicio DATE NULL,
    dtfim DATE NULL,
    imagem VARCHAR(255) NULL,
    link VARCHAR(255) NULL,
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NULL DEFAULT NULL,
    INDEX idx_clientes_id (clientes_id),
    INDEX idx_categorias_id (categorias_id)
);

-- Inserir dados iniciais em categorias
INSERT IGNORE INTO categorias (id, nome, created_at, updated_at) VALUES 
(1, 'Alimentação', NOW(), NOW()),
(2, 'Combustível', NOW(), NOW()),
(3, 'Farmácia', NOW(), NOW()),
(4, 'Supermercado', NOW(), NOW()),
(5, 'Varejo', NOW(), NOW());

-- Inserir dados de exemplo em promocoes
INSERT IGNORE INTO promocoes (clientes_id, categorias_id, titulo, dtinicio, dtfim, created_at, updated_at) VALUES 
(1, 2, 'Promoção Combustível', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), NOW(), NOW()),
(2, 1, 'Desconto Alimentação', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 15 DAY), NOW(), NOW()),
(3, 4, 'Oferta Supermercado', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY), NOW(), NOW());

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePromocoesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('promocoes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('clientes_id');
            $table->unsignedBigInteger('categorias_id');
            $table->string('titulo');
            $table->date('dtinicio')->nullable();
            $table->date('dtfim')->nullable();
            $table->string('imagem')->nullable();
            $table->string('link')->nullable();
            $table->timestamps();

            $table->index('clientes_id');
            $table->index('categorias_id');
        });

        // Inserir dados de exemplo
        DB::table('promocoes')->insert([
            [
                'clientes_id' => 1,
                'categorias_id' => 2,
                'titulo' => 'Promoção Combustível',
                'dtinicio' => now()->format('Y-m-d'),
                'dtfim' => now()->addDays(30)->format('Y-m-d'),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'clientes_id' => 2,
                'categorias_id' => 1,
                'titulo' => 'Desconto Alimentação',
                'dtinicio' => now()->format('Y-m-d'),
                'dtfim' => now()->addDays(15)->format('Y-m-d'),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'clientes_id' => 3,
                'categorias_id' => 4,
                'titulo' => 'Oferta Supermercado',
                'dtinicio' => now()->format('Y-m-d'),
                'dtfim' => now()->addDays(7)->format('Y-m-d'),
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('promocoes');
    }
}

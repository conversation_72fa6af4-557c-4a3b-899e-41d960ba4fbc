
name: "Coding Standards"

on:
  pull_request:
    branches:
      - "*.x"
  push:
    branches:
      - "*.x"

env:
  COMPOSER_ROOT_VERSION: "1.4"

jobs:
  coding-standards:
    name: "Coding Standards"
    runs-on: "ubuntu-20.04"

    strategy:
      matrix:
        php-version:
          - "7.4"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "${{ matrix.php-version }}"
          tools: "cs2pr"

      - name: "Cache dependencies installed with Composer"
        uses: "actions/cache@v2"
        with:
          path: "~/.composer/cache"
          key: "php-${{ matrix.php-version }}-composer-locked-${{ hashFiles('composer.lock') }}"
          restore-keys: "php-${{ matrix.php-version }}-composer-locked-"

      - name: "Install dependencies with Composer"
        run: "composer install --no-interaction --no-progress"

      # https://github.com/doctrine/.github/issues/3
      - name: "Run PHP_CodeSniffer"
        run: "vendor/bin/phpcs -q --no-colors --report=checkstyle | cs2pr"

{"packages": [{"name": "asm89/stack-cors", "version": "v2.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/asm89/stack-cors.git", "reference": "9cb795bf30988e8c96dd3c40623c48a877bc6714"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/asm89/stack-cors/zipball/9cb795bf30988e8c96dd3c40623c48a877bc6714", "reference": "9cb795bf30988e8c96dd3c40623c48a877bc6714", "shasum": ""}, "require": {"php": "^7.0|^8.0", "symfony/http-foundation": "~2.7|~3.0|~4.0|~5.0", "symfony/http-kernel": "~2.7|~3.0|~4.0|~5.0"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "squizlabs/php_codesniffer": "^3.5"}, "time": "2021-03-11T06:42:03+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Asm89\\Stack\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library and stack middleware", "homepage": "https://github.com/asm89/stack-cors", "keywords": ["cors", "stack"], "install-path": "../asm89/stack-cors"}, {"name": "brick/math", "version": "0.9.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "dff976c2f3487d42c1db75a3b180e2b9f0e72ce0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/dff976c2f3487d42c1db75a3b180e2b9f0e72ce0", "reference": "dff976c2f3487d42c1db75a3b180e2b9f0e72ce0", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.0", "vimeo/psalm": "4.3.2"}, "time": "2021-01-20T22:51:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "funding": [{"url": "https://tidelift.com/funding/github/packagist/brick/math", "type": "tidelift"}], "install-path": "../brick/math"}, {"name": "chillerlan/php-qrcode", "version": "4.3.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/chillerlan/php-qrcode.git", "reference": "2ca4bf5ae048af1981d1023ee42a0a2a9d51e51d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/chillerlan/php-qrcode/zipball/2ca4bf5ae048af1981d1023ee42a0a2a9d51e51d", "reference": "2ca4bf5ae048af1981d1023ee42a0a2a9d51e51d", "shasum": ""}, "require": {"chillerlan/php-settings-container": "^2.1.4", "ext-mbstring": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"phan/phan": "^5.3", "phpunit/phpunit": "^9.5", "setasign/fpdf": "^1.8.2"}, "suggest": {"chillerlan/php-authenticator": "Yet another Google authenticator! Also creates URIs for mobile apps.", "setasign/fpdf": "Required to use the QR FPDF output."}, "time": "2022-07-25T09:12:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"chillerlan\\QRCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/kazu<PERSON><PERSON>se"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/codemasher"}, {"name": "Contributors", "homepage": "https://github.com/chillerlan/php-qrcode/graphs/contributors"}], "description": "A QR code generator. PHP 7.4+", "homepage": "https://github.com/chillerlan/php-qrcode", "keywords": ["phpqrcode", "qr", "qr code", "qrcode", "qrcode-generator"], "support": {"issues": "https://github.com/chillerlan/php-qrcode/issues", "source": "https://github.com/chillerlan/php-qrcode/tree/4.3.4"}, "funding": [{"url": "https://www.paypal.com/donate?hosted_button_id=WLYUNAT9ZTJZ4", "type": "custom"}, {"url": "https://ko-fi.com/codemasher", "type": "ko_fi"}], "install-path": "../chillerlan/php-qrcode"}, {"name": "chillerlan/php-settings-container", "version": "2.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/chillerlan/php-settings-container.git", "reference": "1beb7df3c14346d4344b0b2e12f6f9a74feabd4a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/chillerlan/php-settings-container/zipball/1beb7df3c14346d4344b0b2e12f6f9a74feabd4a", "reference": "1beb7df3c14346d4344b0b2e12f6f9a74feabd4a", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"phan/phan": "^5.3", "phpunit/phpunit": "^9.5"}, "time": "2022-07-05T22:32:14+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"chillerlan\\Settings\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/codemasher"}], "description": "A container class for immutable settings objects. Not a DI container. PHP 7.4+", "homepage": "https://github.com/chillerlan/php-settings-container", "keywords": ["PHP7", "Settings", "configuration", "container", "helper"], "support": {"issues": "https://github.com/chillerlan/php-settings-container/issues", "source": "https://github.com/chillerlan/php-settings-container"}, "funding": [{"url": "https://www.paypal.com/donate?hosted_button_id=WLYUNAT9ZTJZ4", "type": "custom"}, {"url": "https://ko-fi.com/codemasher", "type": "ko_fi"}], "install-path": "../chillerlan/php-settings-container"}, {"name": "dnoegel/php-xdg-base-dir", "version": "v0.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dnoegel/php-xdg-base-dir.git", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~7.0|~6.0|~5.0|~4.8.35"}, "time": "2019-12-04T15:06:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "implementation of xdg base directory specification for php", "install-path": "../dnoegel/php-xdg-base-dir"}, {"name": "doctrine/inflector", "version": "2.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/9cf661f4eb38f7c881cac67c75ea9b00bf97b210", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^7.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "time": "2020-05-29T15:13:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "install-path": "../doctrine/inflector"}, {"name": "doctrine/instantiator", "version": "1.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/d56bf6102915de5702778fe20f2de3b2fe570b5b", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13 || 1.0.0-alpha2", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "time": "2020-11-10T18:47:58+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "install-path": "../doctrine/instantiator"}, {"name": "doctrine/lexer", "version": "1.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/e864bbf5904cb8f5bb334f99209b48018522f042", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}, "time": "2020-05-25T17:44:05+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "install-path": "../doctrine/lexer"}, {"name": "dragonmantank/cron-expression", "version": "v2.3.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/65b2d8ee1f10915efb3b55597da3404f096acba2", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2", "shasum": ""}, "require": {"php": "^7.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^6.4|^7.0|^8.0|^9.0"}, "time": "2020-10-13T00:52:37+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "install-path": "../dragonmantank/cron-expression"}, {"name": "egulias/email-validator", "version": "2.1.25", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/0dbf5d78455d4d6a41d186da50adc1122ec066f4", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "time": "2020-12-29T14:50:06+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "funding": [{"url": "https://github.com/egulias", "type": "github"}], "install-path": "../egulias/email-validator"}, {"name": "ezyang/htmlpurifier", "version": "v4.13.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"simpletest/simpletest": "dev-master#72de02a7b80c6bb8864ef9bf66d41d2f58f826bd"}, "time": "2020-06-29T00:56:53+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"], "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "install-path": "../ezyang/htmlpurifier"}, {"name": "facade/flare-client-php", "version": "1.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/facade/flare-client-php.git", "reference": "ef0f5bce23b30b32d98fd9bb49c6fa37b40eb546"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facade/flare-client-php/zipball/ef0f5bce23b30b32d98fd9bb49c6fa37b40eb546", "reference": "ef0f5bce23b30b32d98fd9bb49c6fa37b40eb546", "shasum": ""}, "require": {"facade/ignition-contracts": "~1.0", "illuminate/pipeline": "^5.5|^6.0|^7.0|^8.0", "php": "^7.1|^8.0", "symfony/http-foundation": "^3.3|^4.1|^5.0", "symfony/mime": "^3.4|^4.0|^5.1", "symfony/var-dumper": "^3.4|^4.0|^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.14", "phpunit/phpunit": "^7.5.16", "spatie/phpunit-snapshot-assertions": "^2.0"}, "time": "2021-02-16T12:42:06+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Facade\\FlareClient\\": "src"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Send PHP errors to <PERSON><PERSON><PERSON>", "homepage": "https://github.com/facade/flare-client-php", "keywords": ["exception", "facade", "flare", "reporting"], "funding": [{"url": "https://github.com/spatie", "type": "github"}], "install-path": "../facade/flare-client-php"}, {"name": "facade/ignition", "version": "2.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/facade/ignition.git", "reference": "4be10a998815f77952b9eb983fb0b64c8a4defbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facade/ignition/zipball/4be10a998815f77952b9eb983fb0b64c8a4defbb", "reference": "4be10a998815f77952b9eb983fb0b64c8a4defbb", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "facade/flare-client-php": "^1.3.7", "facade/ignition-contracts": "^1.0.2", "filp/whoops": "^2.4", "illuminate/support": "^7.0|^8.0", "monolog/monolog": "^2.0", "php": "^7.2.5|^8.0", "symfony/console": "^5.0", "symfony/var-dumper": "^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.14", "mockery/mockery": "^1.3", "orchestra/testbench": "^5.0|^6.0", "psalm/plugin-laravel": "^1.2"}, "suggest": {"laravel/telescope": "^3.1"}, "time": "2021-03-24T18:58:31+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "laravel": {"providers": ["Facade\\Ignition\\IgnitionServiceProvider"], "aliases": {"Flare": "Facade\\Ignition\\Facades\\Flare"}}}, "installation-source": "dist", "autoload": {"psr-4": {"Facade\\Ignition\\": "src"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A beautiful error page for Laravel applications.", "homepage": "https://github.com/facade/ignition", "keywords": ["error", "flare", "laravel", "page"], "install-path": "../facade/ignition"}, {"name": "facade/ignition-contracts", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/facade/ignition-contracts.git", "reference": "3c921a1cdba35b68a7f0ccffc6dffc1995b18267"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facade/ignition-contracts/zipball/3c921a1cdba35b68a7f0ccffc6dffc1995b18267", "reference": "3c921a1cdba35b68a7f0ccffc6dffc1995b18267", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v2.15.8", "phpunit/phpunit": "^9.3.11", "vimeo/psalm": "^3.17.1"}, "time": "2020-10-16T08:27:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Facade\\IgnitionContracts\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://flareapp.io", "role": "Developer"}], "description": "Solution contracts for Ignition", "homepage": "https://github.com/facade/ignition-contracts", "keywords": ["contracts", "flare", "ignition"], "install-path": "../facade/ignition-contracts"}, {"name": "fideloper/proxy", "version": "4.4.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/fideloper/TrustedProxy.git", "reference": "c073b2bd04d1c90e04dc1b787662b558dd65ade0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fideloper/TrustedProxy/zipball/c073b2bd04d1c90e04dc1b787662b558dd65ade0", "reference": "c073b2bd04d1c90e04dc1b787662b558dd65ade0", "shasum": ""}, "require": {"illuminate/contracts": "^5.0|^6.0|^7.0|^8.0|^9.0", "php": ">=5.4.0"}, "require-dev": {"illuminate/http": "^5.0|^6.0|^7.0|^8.0|^9.0", "mockery/mockery": "^1.0", "phpunit/phpunit": "^6.0"}, "time": "2020-10-22T13:48:01+00:00", "type": "library", "extra": {"laravel": {"providers": ["Fideloper\\Proxy\\TrustedProxyServiceProvider"]}}, "installation-source": "dist", "autoload": {"psr-4": {"Fideloper\\Proxy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Set trusted proxies for <PERSON><PERSON>", "keywords": ["load balancing", "proxy", "trusted proxy"], "install-path": "../fideloper/proxy"}, {"name": "filp/whoops", "version": "2.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/filp/whoops.git", "reference": "f6e14679f948d8a5cfb866fa7065a30c66bd64d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/filp/whoops/zipball/f6e14679f948d8a5cfb866fa7065a30c66bd64d3", "reference": "f6e14679f948d8a5cfb866fa7065a30c66bd64d3", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "psr/log": "^1.0.1"}, "require-dev": {"mockery/mockery": "^0.9 || ^1.0", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.3", "symfony/var-dumper": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}, "time": "2021-03-19T12:00:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "description": "php error handling for cool kids", "homepage": "https://filp.github.io/whoops/", "keywords": ["error", "exception", "handling", "library", "throwable", "whoops"], "funding": [{"url": "https://github.com/denis-so<PERSON><PERSON>", "type": "github"}], "install-path": "../filp/whoops"}, {"name": "fruitcake/laravel-cors", "version": "v2.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/fruitcake/laravel-cors.git", "reference": "01de0fe5f71c70d1930ee9a80385f9cc28e0f63a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fruitcake/laravel-cors/zipball/01de0fe5f71c70d1930ee9a80385f9cc28e0f63a", "reference": "01de0fe5f71c70d1930ee9a80385f9cc28e0f63a", "shasum": ""}, "require": {"asm89/stack-cors": "^2.0.1", "illuminate/contracts": "^6|^7|^8|^9", "illuminate/support": "^6|^7|^8|^9", "php": ">=7.2", "symfony/http-foundation": "^4|^5", "symfony/http-kernel": "^4.3.4|^5"}, "require-dev": {"laravel/framework": "^6|^7|^8", "orchestra/testbench-dusk": "^4|^5|^6", "phpunit/phpunit": "^6|^7|^8", "squizlabs/php_codesniffer": "^3.5"}, "time": "2020-10-22T13:57:20+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}, "laravel": {"providers": ["Fruitcake\\Cors\\CorsServiceProvider"]}}, "installation-source": "dist", "autoload": {"psr-4": {"Fruitcake\\Cors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Fruitcake", "homepage": "https://fruitcake.nl"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Adds CORS (Cross-Origin Resource Sharing) headers support in your Laravel application", "keywords": ["api", "cors", "crossdomain", "laravel"], "funding": [{"url": "https://github.com/barryvdh", "type": "github"}], "install-path": "../fruitcake/laravel-cors"}, {"name": "fzaninotto/faker", "version": "v1.9.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^2.9.2"}, "time": "2020-12-11T09:56:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "abandoned": true, "install-path": "../fzaninotto/faker"}, {"name": "guzzlehttp/guzzle", "version": "6.5.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "time": "2020-06-16T21:01:06+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "install-path": "../guzzlehttp/guzzle"}, {"name": "guzzlehttp/promises", "version": "1.4.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "8e7d04f1f6450fef59366c399cfad4b9383aa30d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/8e7d04f1f6450fef59366c399cfad4b9383aa30d", "reference": "8e7d04f1f6450fef59366c399cfad4b9383aa30d", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "time": "2021-03-07T09:25:29+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "install-path": "../guzzlehttp/promises"}, {"name": "guzzlehttp/psr7", "version": "1.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "35ea11d335fd638b5882ff1725228b3d35496ab1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/35ea11d335fd638b5882ff1725228b3d35496ab1", "reference": "35ea11d335fd638b5882ff1725228b3d35496ab1", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2021-03-21T16:25:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "install-path": "../guzzlehttp/psr7"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "time": "2020-07-09T08:09:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "install-path": "../hamcrest/hamcrest-php"}, {"name": "intervention/image", "version": "2.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "abbf18d5ab8367f96b3205ca3c89fb2fa598c69e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/abbf18d5ab8367f96b3205ca3c89fb2fa598c69e", "reference": "abbf18d5ab8367f96b3205ca3c89fb2fa598c69e", "shasum": ""}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "time": "2019-11-02T09:15:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}, "laravel": {"providers": ["Intervention\\Image\\ImageServiceProvider"], "aliases": {"Image": "Intervention\\Image\\Facades\\Image"}}}, "installation-source": "dist", "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://olivervogel.com/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "install-path": "../intervention/image"}, {"name": "laravel/framework", "version": "v7.30.4", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/laravel/framework.git", "reference": "9dd38140dc2924daa1a020a3d7a45f9ceff03df3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/framework/zipball/9dd38140dc2924daa1a020a3d7a45f9ceff03df3", "reference": "9dd38140dc2924daa1a020a3d7a45f9ceff03df3", "shasum": ""}, "require": {"doctrine/inflector": "^1.4|^2.0", "dragonmantank/cron-expression": "^2.3.1", "egulias/email-validator": "^2.1.10", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "league/commonmark": "^1.3", "league/flysystem": "^1.1", "monolog/monolog": "^2.0", "nesbot/carbon": "^2.31", "opis/closure": "^3.6", "php": "^7.2.5|^8.0", "psr/container": "^1.0", "psr/simple-cache": "^1.0", "ramsey/uuid": "^3.7|^4.0", "swiftmailer/swiftmailer": "^6.0", "symfony/console": "^5.0", "symfony/error-handler": "^5.0", "symfony/finder": "^5.0", "symfony/http-foundation": "^5.0", "symfony/http-kernel": "^5.0", "symfony/mime": "^5.0", "symfony/polyfill-php73": "^1.17", "symfony/process": "^5.0", "symfony/routing": "^5.0", "symfony/var-dumper": "^5.0", "tijsverkoyen/css-to-inline-styles": "^2.2.2", "vlucas/phpdotenv": "^4.0", "voku/portable-ascii": "^1.4.8"}, "conflict": {"tightenco/collect": "<5.5.33"}, "provide": {"psr/container-implementation": "1.0"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/testing": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version"}, "require-dev": {"aws/aws-sdk-php": "^3.155", "doctrine/dbal": "^2.6", "filp/whoops": "^2.8", "guzzlehttp/guzzle": "^6.3.1|^7.0.1", "league/flysystem-cached-adapter": "^1.0", "mockery/mockery": "~1.3.3|^1.4.2", "moontoast/math": "^1.1", "orchestra/testbench-core": "^5.8", "pda/pheanstalk": "^4.0", "phpunit/phpunit": "^8.4|^9.3.3", "predis/predis": "^1.1.1", "symfony/cache": "^5.0"}, "suggest": {"aws/aws-sdk-php": "Required to use the SQS queue driver, DynamoDb failed job storage and SES mail driver (^3.155).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.6).", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "ext-memcached": "Required to use the memcache cache driver.", "ext-pcntl": "Required to use all features of the queue worker.", "ext-posix": "Required to use all features of the queue worker.", "ext-redis": "Required to use the Redis cache and queue drivers (^4.0|^5.0).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.9.1).", "filp/whoops": "Required for friendly error pages in development (^2.8).", "guzzlehttp/guzzle": "Required to use the HTTP Client, Mailgun mail driver and the ping methods on schedules (^6.3.1|^7.0.1).", "laravel/tinker": "Required to use the tinker console command (^2.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^1.0).", "league/flysystem-cached-adapter": "Required to use the Flysystem cache (^1.0).", "league/flysystem-sftp": "Required to use the Flysystem SFTP driver (^1.0).", "mockery/mockery": "Required to use mocking (~1.3.3|^1.4.2).", "moontoast/math": "Required to use ordered UUIDs (^1.1).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "pda/pheanstalk": "Required to use the beanstalk queue driver (^4.0).", "phpunit/phpunit": "Required to use assertions and run tests (^8.4|^9.3.3).", "predis/predis": "Required to use the predis connector (^1.1.2).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^4.0).", "symfony/cache": "Required to PSR-6 cache bridge (^5.0).", "symfony/filesystem": "Required to create relative storage directory symbolic links (^5.0).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^2.0).", "wildbit/swiftmailer-postmark": "Required to use Postmark mail driver (^3.0)."}, "time": "2021-01-21T14:10:48+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "install-path": "../laravel/framework"}, {"name": "laravel/tinker", "version": "v2.6.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/laravel/tinker.git", "reference": "04ad32c1a3328081097a181875733fa51f402083"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/tinker/zipball/04ad32c1a3328081097a181875733fa51f402083", "reference": "04ad32c1a3328081097a181875733fa51f402083", "shasum": ""}, "require": {"illuminate/console": "^6.0|^7.0|^8.0", "illuminate/contracts": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "php": "^7.2.5|^8.0", "psy/psysh": "^0.10.4", "symfony/var-dumper": "^4.3.4|^5.0"}, "require-dev": {"mockery/mockery": "~1.3.3|^1.4.2", "phpunit/phpunit": "^8.5.8|^9.3.3"}, "suggest": {"illuminate/database": "The Illuminate Database package (^6.0|^7.0|^8.0)."}, "time": "2021-03-02T16:53:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "laravel": {"providers": ["Laravel\\Tinker\\TinkerServiceProvider"]}}, "installation-source": "dist", "autoload": {"psr-4": {"Laravel\\Tinker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful REPL for the Laravel framework.", "keywords": ["REPL", "Tinker", "laravel", "psysh"], "install-path": "../laravel/tinker"}, {"name": "league/commonmark", "version": "1.5.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/thephpleague/commonmark.git", "reference": "11df9b36fd4f1d2b727a73bf14931d81373b9a54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/commonmark/zipball/11df9b36fd4f1d2b727a73bf14931d81373b9a54", "reference": "11df9b36fd4f1d2b727a73bf14931d81373b9a54", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^8.0"}, "conflict": {"scrutinizer/ocular": "1.7.*"}, "require-dev": {"cebe/markdown": "~1.0", "commonmark/commonmark.js": "0.29.2", "erusev/parsedown": "~1.0", "ext-json": "*", "github/gfm": "0.29.0", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.4", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.2", "scrutinizer/ocular": "^1.5", "symfony/finder": "^4.2"}, "time": "2020-10-31T13:49:32+00:00", "bin": ["bin/commonmark"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"League\\CommonMark\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and Github-Flavored Markdown (GFM)", "homepage": "https://commonmark.thephpleague.com", "keywords": ["commonmark", "flavored", "gfm", "github", "github-flavored", "markdown", "md", "parser"], "funding": [{"url": "https://enjoy.gitstore.app/repositories/thephpleague/commonmark", "type": "custom"}, {"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://www.patreon.com/colinodell", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "install-path": "../league/commonmark"}, {"name": "league/flysystem", "version": "1.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "9be3b16c877d477357c015cec057548cf9b2a14a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/9be3b16c877d477357c015cec057548cf9b2a14a", "reference": "9be3b16c877d477357c015cec057548cf9b2a14a", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-fileinfo": "Required for MimeType", "ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "time": "2020-08-23T07:39:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "install-path": "../league/flysystem"}, {"name": "league/mime-type-detection", "version": "1.7.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "3b9dff8aaf7323590c1d2e443db701eb1f9aa0d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/3b9dff8aaf7323590c1d2e443db701eb1f9aa0d3", "reference": "3b9dff8aaf7323590c1d2e443db701eb1f9aa0d3", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.18", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3"}, "time": "2021-01-18T20:58:21+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "install-path": "../league/mime-type-detection"}, {"name": "maatwebsite/excel", "version": "3.1.29", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Maatwebsite/Laravel-Excel.git", "reference": "1e567e6e19a04fd65b5876d5bc92f4015f09fab4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Maatwebsite/Laravel-Excel/zipball/1e567e6e19a04fd65b5876d5bc92f4015f09fab4", "reference": "1e567e6e19a04fd65b5876d5bc92f4015f09fab4", "shasum": ""}, "require": {"ext-json": "*", "illuminate/support": "5.8.*|^6.0|^7.0|^8.0", "php": "^7.0|^8.0", "phpoffice/phpspreadsheet": "1.16.*"}, "require-dev": {"orchestra/testbench": "^6.0", "predis/predis": "^1.1"}, "time": "2021-03-16T11:56:39+00:00", "type": "library", "extra": {"laravel": {"providers": ["Maatwebsite\\Excel\\ExcelServiceProvider"], "aliases": {"Excel": "Maatwebsite\\Excel\\Facades\\Excel"}}}, "installation-source": "dist", "autoload": {"psr-4": {"Maatwebsite\\Excel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Supercharged Excel exports and imports in Laravel", "keywords": ["PHPExcel", "batch", "csv", "excel", "export", "import", "laravel", "php", "phpspreadsheet"], "funding": [{"url": "https://laravel-excel.com/commercial-support", "type": "custom"}, {"url": "https://github.com/patrickbrouwers", "type": "github"}], "install-path": "../maatwebsite/excel"}, {"name": "maennchen/zipstream-php", "version": "2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/c4c5803cc1f93df3d2448478ef79394a5981cc58", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58", "shasum": ""}, "require": {"myclabs/php-enum": "^1.5", "php": ">= 7.1", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "guzzlehttp/guzzle": ">= 6.3", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": ">= 7.5"}, "time": "2020-05-30T13:11:16+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "funding": [{"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "install-path": "../maennchen/zipstream-php"}, {"name": "markbaker/complex", "version": "2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "9999f1432fae467bc93c53f357105b4c31bb994c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/9999f1432fae467bc93c53f357105b4c31bb994c", "reference": "9999f1432fae467bc93c53f357105b4c31bb994c", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.4"}, "time": "2020-08-26T10:42:07+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Complex\\": "classes/src/"}, "files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "install-path": "../markbaker/complex"}, {"name": "markbaker/matrix", "version": "2.1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "361c0f545c3172ee26c3d596a0aa03f0cef65e6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/361c0f545c3172ee26c3d596a0aa03f0cef65e6a", "reference": "361c0f545c3172ee26c3d596a0aa03f0cef65e6a", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.4"}, "time": "2021-01-23T16:37:31+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}, "files": ["classes/src/Functions/adjoint.php", "classes/src/Functions/antidiagonal.php", "classes/src/Functions/cofactors.php", "classes/src/Functions/determinant.php", "classes/src/Functions/diagonal.php", "classes/src/Functions/identity.php", "classes/src/Functions/inverse.php", "classes/src/Functions/minors.php", "classes/src/Functions/trace.php", "classes/src/Functions/transpose.php", "classes/src/Operations/add.php", "classes/src/Operations/directsum.php", "classes/src/Operations/subtract.php", "classes/src/Operations/multiply.php", "classes/src/Operations/divideby.php", "classes/src/Operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "install-path": "../markbaker/matrix"}, {"name": "mockery/mockery", "version": "1.4.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "d1339f64479af1bee0e82a0413813fe5345a54ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/d1339f64479af1bee0e82a0413813fe5345a54ea", "reference": "d1339f64479af1bee0e82a0413813fe5345a54ea", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": "^7.3 || ^8.0"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.3"}, "time": "2021-02-24T09:51:49+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "installation-source": "dist", "autoload": {"psr-0": {"Mockery": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "install-path": "../mockery/mockery"}, {"name": "monolog/monolog", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "1cb1cde8e8dd0f70cc0fe51354a59acad9302084"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/1cb1cde8e8dd0f70cc0fe51354a59acad9302084", "reference": "1cb1cde8e8dd0f70cc0fe51354a59acad9302084", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "graylog2/gelf-php": "^1.4.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90 <7.0.1", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "time": "2020-12-14T13:15:25+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "install-path": "../monolog/monolog"}, {"name": "myclabs/deep-copy", "version": "1.10.2", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/776f831124e9c62e1a2c601ecc52e776d8bb7220", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "time": "2020-11-13T09:40:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "install-path": "../myclabs/deep-copy"}, {"name": "myclabs/php-enum", "version": "1.8.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "46cf3d8498b095bd33727b13fd5707263af99421"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/46cf3d8498b095bd33727b13fd5707263af99421", "reference": "46cf3d8498b095bd33727b13fd5707263af99421", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.5.1"}, "time": "2021-02-15T16:11:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "install-path": "../myclabs/php-enum"}, {"name": "nesbot/carbon", "version": "2.46.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "2fd2c4a77d58a4e95234c8a61c5df1f157a91bf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/2fd2c4a77d58a4e95234c8a61c5df1f157a91bf4", "reference": "2fd2c4a77d58a4e95234c8a61c5df1f157a91bf4", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^3.4 || ^4.0 || ^5.0"}, "require-dev": {"doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.54", "phpunit/phpunit": "^7.5.20 || ^8.5.14", "squizlabs/php_codesniffer": "^3.4"}, "time": "2021-02-24T17:30:44+00:00", "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev", "dev-3.x": "3.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "installation-source": "dist", "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}, {"name": "kylekatarnls", "homepage": "http://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "funding": [{"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "install-path": "../nesbot/carbon"}, {"name": "nikic/php-parser", "version": "v4.10.4", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "c6d052fc58cb876152f89f532b95a8d7907e7f0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/c6d052fc58cb876152f89f532b95a8d7907e7f0e", "reference": "c6d052fc58cb876152f89f532b95a8d7907e7f0e", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "time": "2020-12-20T10:01:03+00:00", "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "install-path": "../nikic/php-parser"}, {"name": "nunomaduro/collision", "version": "v4.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nunomaduro/collision.git", "reference": "7c125dc2463f3e144ddc7e05e63077109508c94e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/collision/zipball/7c125dc2463f3e144ddc7e05e63077109508c94e", "reference": "7c125dc2463f3e144ddc7e05e63077109508c94e", "shasum": ""}, "require": {"facade/ignition-contracts": "^1.0", "filp/whoops": "^2.4", "php": "^7.2.5 || ^8.0", "symfony/console": "^5.0"}, "require-dev": {"facade/ignition": "^2.0", "fideloper/proxy": "^4.2", "friendsofphp/php-cs-fixer": "^2.16", "fruitcake/laravel-cors": "^1.0", "laravel/framework": "^7.0", "laravel/tinker": "^2.0", "nunomaduro/larastan": "^0.6", "orchestra/testbench": "^5.0", "phpstan/phpstan": "^0.12.3", "phpunit/phpunit": "^8.5.1 || ^9.0"}, "time": "2020-10-29T15:12:23+00:00", "type": "library", "extra": {"laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}}, "installation-source": "dist", "autoload": {"psr-4": {"NunoMaduro\\Collision\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Cli error handling for console/command-line PHP applications.", "keywords": ["artisan", "cli", "command-line", "console", "error", "handling", "laravel", "laravel-zero", "php", "symfony"], "funding": [{"url": "https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=66BYDWAT92N6L", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://www.patreon.com/nunomaduro", "type": "patreon"}], "install-path": "../nunomaduro/collision"}, {"name": "opis/closure", "version": "3.6.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "943b5d70cc5ae7483f6aff6ff43d7e34592ca0f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/943b5d70cc5ae7483f6aff6ff43d7e34592ca0f5", "reference": "943b5d70cc5ae7483f6aff6ff43d7e34592ca0f5", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "time": "2020-11-07T02:01:34+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Opis\\Closure\\": "src/"}, "files": ["functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "install-path": "../opis/closure"}, {"name": "phar-io/manifest", "version": "2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "85265efd3af7ba3ca4b2a2c34dbfc5788dd29133"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/85265efd3af7ba3ca4b2a2c34dbfc5788dd29133", "reference": "85265efd3af7ba3ca4b2a2c34dbfc5788dd29133", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "time": "2020-06-27T14:33:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "install-path": "../phar-io/manifest"}, {"name": "phar-io/version", "version": "3.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "bae7c545bef187884426f042434e561ab1ddb182"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/bae7c545bef187884426f042434e561ab1ddb182", "reference": "bae7c545bef187884426f042434e561ab1ddb182", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2021-02-23T14:00:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "install-path": "../phar-io/version"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2020-06-27T09:03:43+00:00", "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "install-path": "../phpdocumentor/reflection-common"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.2.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "069a785b2141f5bcf49f3e353548dc1cce6df556"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/069a785b2141f5bcf49f3e353548dc1cce6df556", "reference": "069a785b2141f5bcf49f3e353548dc1cce6df556", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2"}, "time": "2020-09-03T19:13:55+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "install-path": "../phpdocumentor/reflection-docblock"}, {"name": "phpdocumentor/type-resolver", "version": "1.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*"}, "time": "2020-09-17T18:55:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "install-path": "../phpdocumentor/type-resolver"}, {"name": "phpoffice/phpspreadsheet", "version": "1.16.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "76d4323b85129d0c368149c831a07a3e258b2b50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/76d4323b85129d0c368149c831a07a3e258b2b50", "reference": "76d4323b85129d0c368149c831a07a3e258b2b50", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.13", "maennchen/zipstream-php": "^2.1", "markbaker/complex": "^1.5||^2.0", "markbaker/matrix": "^1.2||^2.0", "php": "^7.2||^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0"}, "require-dev": {"dompdf/dompdf": "^0.8.5", "friendsofphp/php-cs-fixer": "^2.16", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^8.5||^9.3", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)"}, "time": "2020-12-31T18:03:49+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "install-path": "../phpoffice/phpspreadsheet"}, {"name": "phpoption/phpoption", "version": "1.7.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "994ecccd8f3283ecf5ac33254543eb0ac946d525"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/994ecccd8f3283ecf5ac33254543eb0ac946d525", "reference": "994ecccd8f3283ecf5ac33254543eb0ac946d525", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^4.8.35 || ^5.7.27 || ^6.5.6 || ^7.0 || ^8.0 || ^9.0"}, "time": "2020-07-20T17:29:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "install-path": "../phpoption/phpoption"}, {"name": "phpspec/prophecy", "version": "1.13.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "be1996ed8adc35c3fd795488a653f4b518be70ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/be1996ed8adc35c3fd795488a653f4b518be70ea", "reference": "be1996ed8adc35c3fd795488a653f4b518be70ea", "shasum": ""}, "require": {"doctrine/instantiator": "^1.2", "php": "^7.2 || ~8.0, <8.1", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0", "sebastian/recursion-context": "^3.0 || ^4.0"}, "require-dev": {"phpspec/phpspec": "^6.0", "phpunit/phpunit": "^8.0 || ^9.0"}, "time": "2021-03-17T13:42:18+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.11.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "install-path": "../phpspec/prophecy"}, {"name": "phpunit/php-code-coverage", "version": "7.0.14", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "bb7c9a210c72e4709cdde67f8b7362f672f2225c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/bb7c9a210c72e4709cdde67f8b7362f672f2225c", "reference": "bb7c9a210c72e4709cdde67f8b7362f672f2225c", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": ">=7.2", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.1.1 || ^4.0", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}, "require-dev": {"phpunit/phpunit": "^8.2.2"}, "suggest": {"ext-xdebug": "^2.7.2"}, "time": "2020-12-02T13:39:03+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-code-coverage"}, {"name": "phpunit/php-file-iterator", "version": "2.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "4b49fb70f067272b659ef0174ff9ca40fdaa6357"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/4b49fb70f067272b659ef0174ff9ca40fdaa6357", "reference": "4b49fb70f067272b659ef0174ff9ca40fdaa6357", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "time": "2020-11-30T08:25:21+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-file-iterator"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "time": "2015-06-21T13:50:34+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "install-path": "../phpunit/php-text-template"}, {"name": "phpunit/php-timer", "version": "2.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "2454ae1765516d20c4ffe103d85a58a9a3bd5662"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/2454ae1765516d20c4ffe103d85a58a9a3bd5662", "reference": "2454ae1765516d20c4ffe103d85a58a9a3bd5662", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "time": "2020-11-30T08:20:02+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-timer"}, {"name": "phpunit/php-token-stream", "version": "4.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "a853a0e183b9db7eed023d7933a858fa1c8d25a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/a853a0e183b9db7eed023d7933a858fa1c8d25a3", "reference": "a853a0e183b9db7eed023d7933a858fa1c8d25a3", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "time": "2020-08-04T08:28:15+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "abandoned": true, "install-path": "../phpunit/php-token-stream"}, {"name": "phpunit/phpunit", "version": "8.5.15", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "038d4196d8e8cb405cd5e82cedfe413ad6eef9ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/038d4196d8e8cb405cd5e82cedfe413ad6eef9ef", "reference": "038d4196d8e8cb405cd5e82cedfe413ad6eef9ef", "shasum": ""}, "require": {"doctrine/instantiator": "^1.3.1", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.10.0", "phar-io/manifest": "^2.0.1", "phar-io/version": "^3.0.2", "php": ">=7.2", "phpspec/prophecy": "^1.10.3", "phpunit/php-code-coverage": "^7.0.12", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1.2", "sebastian/comparator": "^3.0.2", "sebastian/diff": "^3.0.2", "sebastian/environment": "^4.2.3", "sebastian/exporter": "^3.1.2", "sebastian/global-state": "^3.0.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0.1", "sebastian/type": "^1.1.3", "sebastian/version": "^2.0.1"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*", "phpunit/php-invoker": "^2.0.0"}, "time": "2021-03-17T07:27:54+00:00", "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "8.5-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "funding": [{"url": "https://phpunit.de/donate.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/phpunit"}, {"name": "psr/container", "version": "1.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/8622567409010282b7aeebe4bb841fe98b58dcaf", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf", "shasum": ""}, "require": {"php": ">=7.2.0"}, "time": "2021-03-05T17:36:06+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "install-path": "../psr/container"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "time": "2019-01-08T18:20:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "install-path": "../psr/event-dispatcher"}, {"name": "psr/http-client", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "time": "2020-06-29T06:28:15+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "install-path": "../psr/http-client"}, {"name": "psr/http-factory", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "time": "2019-04-30T12:38:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "install-path": "../psr/http-factory"}, {"name": "psr/http-message", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T14:39:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "install-path": "../psr/http-message"}, {"name": "psr/log", "version": "1.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2020-03-23T09:12:05+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "install-path": "../psr/log"}, {"name": "psr/simple-cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2017-10-23T01:57:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "install-path": "../psr/simple-cache"}, {"name": "psy/psysh", "version": "v0.10.7", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "a395af46999a12006213c0c8346c9445eb31640c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/a395af46999a12006213c0c8346c9445eb31640c", "reference": "a395af46999a12006213c0c8346c9445eb31640c", "shasum": ""}, "require": {"dnoegel/php-xdg-base-dir": "0.1.*", "ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "~4.0|~3.0|~2.0|~1.3", "php": "^8.0 || ^7.0 || ^5.5.9", "symfony/console": "~5.0|~4.0|~3.0|^2.4.2|~2.3.10", "symfony/var-dumper": "~5.0|~4.0|~3.0|~2.7"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "hoa/console": "3.17.*"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "hoa/console": "A pure PHP readline implementation. You'll want this if your PHP install doesn't already support readline or libedit."}, "time": "2021-03-14T02:14:56+00:00", "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-main": "0.10.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "install-path": "../psy/psysh"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "install-path": "../ralouphie/getallheaders"}, {"name": "ramsey/collection", "version": "1.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "28a5c4ab2f5111db6a60b2b4ec84057e0f43b9c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/28a5c4ab2f5111db6a60b2b4ec84057e0f43b9c1", "reference": "28a5c4ab2f5111db6a60b2b4ec84057e0f43b9c1", "shasum": ""}, "require": {"php": "^7.2 || ^8"}, "require-dev": {"captainhook/captainhook": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "ergebnis/composer-normalize": "^2.6", "fakerphp/faker": "^1.5", "hamcrest/hamcrest-php": "^2", "jangregor/phpstan-prophecy": "^0.8", "mockery/mockery": "^1.3", "phpstan/extension-installer": "^1", "phpstan/phpstan": "^0.12.32", "phpstan/phpstan-mockery": "^0.12.5", "phpstan/phpstan-phpunit": "^0.12.11", "phpunit/phpunit": "^8.5 || ^9", "psy/psysh": "^0.10.4", "slevomat/coding-standard": "^6.3", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.4"}, "time": "2021-01-21T17:40:04+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP 7.2+ library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/collection", "type": "tidelift"}], "install-path": "../ramsey/collection"}, {"name": "ramsey/uuid", "version": "4.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "cd4032040a750077205918c86049aa0f43d22947"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/cd4032040a750077205918c86049aa0f43d22947", "reference": "cd4032040a750077205918c86049aa0f43d22947", "shasum": ""}, "require": {"brick/math": "^0.8 || ^0.9", "ext-json": "*", "php": "^7.2 || ^8", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"codeception/aspect-mock": "^3", "dealerdirect/phpcodesniffer-composer-installer": "^0.6.2 || ^0.7.0", "doctrine/annotations": "^1.8", "goaop/framework": "^2", "mockery/mockery": "^1.3", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock-mockery": "^1.3", "php-mock/php-mock-phpunit": "^2.5", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^0.17.1", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5", "psy/psysh": "^0.10.0", "slevomat/coding-standard": "^6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "3.9.4"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-ctype": "Enables faster processing of character classification using ctype functions.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "time": "2020-08-18T17:17:46+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Ramsey\\Uuid\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "homepage": "https://github.com/ramsey/uuid", "keywords": ["guid", "identifier", "uuid"], "funding": [{"url": "https://github.com/ramsey", "type": "github"}], "install-path": "../ramsey/uuid"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/1de8cd5c010cb153fcd68b8d0f64606f523f7619", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "time": "2020-11-30T08:15:22+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/code-unit-reverse-lookup"}, {"name": "sebastian/comparator", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "1071dfcef776a57013124ff35e1fc41ccd294758"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/1071dfcef776a57013124ff35e1fc41ccd294758", "reference": "1071dfcef776a57013124ff35e1fc41ccd294758", "shasum": ""}, "require": {"php": ">=7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "time": "2020-11-30T08:04:30+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/comparator"}, {"name": "sebastian/diff", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "14f72dd46eaf2f2293cbe79c93cc0bc43161a211"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/14f72dd46eaf2f2293cbe79c93cc0bc43161a211", "reference": "14f72dd46eaf2f2293cbe79c93cc0bc43161a211", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "time": "2020-11-30T07:59:04+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/diff"}, {"name": "sebastian/environment", "version": "4.2.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "d47bbbad83711771f167c72d4e3f25f7fcc1f8b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/d47bbbad83711771f167c72d4e3f25f7fcc1f8b0", "reference": "d47bbbad83711771f167c72d4e3f25f7fcc1f8b0", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "time": "2020-11-30T07:53:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/environment"}, {"name": "sebastian/exporter", "version": "3.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "6b853149eab67d4da22291d36f5b0631c0fd856e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/6b853149eab67d4da22291d36f5b0631c0fd856e", "reference": "6b853149eab67d4da22291d36f5b0631c0fd856e", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^6.0"}, "time": "2020-11-30T07:47:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/exporter"}, {"name": "sebastian/global-state", "version": "3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "474fb9edb7ab891665d3bfc6317f42a0a150454b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/474fb9edb7ab891665d3bfc6317f42a0a150454b", "reference": "474fb9edb7ab891665d3bfc6317f42a0a150454b", "shasum": ""}, "require": {"php": ">=7.2", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^8.0"}, "suggest": {"ext-uopz": "*"}, "time": "2020-11-30T07:43:24+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/global-state"}, {"name": "sebastian/object-enumerator", "version": "3.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/object-enumerator/zipball/e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "time": "2020-11-30T07:40:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/object-enumerator"}, {"name": "sebastian/object-reflector", "version": "1.1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "time": "2020-11-30T07:37:18+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/object-reflector"}, {"name": "sebastian/recursion-context", "version": "3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/367dcba38d6e1977be014dc4b22f47a484dac7fb", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "time": "2020-11-30T07:34:24+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/recursion-context"}, {"name": "sebastian/resource-operations", "version": "2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "31d35ca87926450c44eae7e2611d45a7a65ea8b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/31d35ca87926450c44eae7e2611d45a7a65ea8b3", "reference": "31d35ca87926450c44eae7e2611d45a7a65ea8b3", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2020-11-30T07:30:19+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/resource-operations"}, {"name": "sebastian/type", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "0150cfbc4495ed2df3872fb31b26781e4e077eb4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/0150cfbc4495ed2df3872fb31b26781e4e077eb4", "reference": "0150cfbc4495ed2df3872fb31b26781e4e077eb4", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"phpunit/phpunit": "^8.2"}, "time": "2020-11-30T07:25:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/type"}, {"name": "sebastian/version", "version": "2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "time": "2016-10-03T07:35:21+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "install-path": "../sebastian/version"}, {"name": "swiftmailer/swiftmailer", "version": "v6.2.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "15f7faf8508e04471f666633addacf54c0ab5933"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/15f7faf8508e04471f666633addacf54c0ab5933", "reference": "15f7faf8508e04471f666633addacf54c0ab5933", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.0"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "time": "2021-03-09T12:30:35+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "installation-source": "dist", "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "install-path": "../swiftmailer/swiftmailer"}, {"name": "symfony/console", "version": "v5.2.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "938ebbadae1b0a9c9d1ec313f87f9708609f1b79"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/938ebbadae1b0a9c9d1ec313f87f9708609f1b79", "reference": "938ebbadae1b0a9c9d1ec313f87f9708609f1b79", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2", "symfony/string": "^5.1"}, "conflict": {"symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "symfony/var-dumper": "^4.4|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "time": "2021-03-06T13:42:15+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command line", "console", "terminal"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/console"}, {"name": "symfony/css-selector", "version": "v5.2.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "f65f217b3314504a1ec99c2d6ef69016bb13490f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/f65f217b3314504a1ec99c2d6ef69016bb13490f", "reference": "f65f217b3314504a1ec99c2d6ef69016bb13490f", "shasum": ""}, "require": {"php": ">=7.2.5"}, "time": "2021-01-27T10:01:46+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/css-selector"}, {"name": "symfony/deprecation-contracts", "version": "v2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "5fa56b4074d1ae755beb55617ddafe6f5d78f665"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/5fa56b4074d1ae755beb55617ddafe6f5d78f665", "reference": "5fa56b4074d1ae755beb55617ddafe6f5d78f665", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2020-09-07T11:33:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/error-handler", "version": "v5.2.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "b547d3babcab5c31e01de59ee33e9d9c1421d7d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/b547d3babcab5c31e01de59ee33e9d9c1421d7d0", "reference": "b547d3babcab5c31e01de59ee33e9d9c1421d7d0", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1.0", "symfony/polyfill-php80": "^1.15", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"symfony/deprecation-contracts": "^2.1", "symfony/http-kernel": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "time": "2021-02-11T08:21:20+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/error-handler"}, {"name": "symfony/event-dispatcher", "version": "v5.2.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "d08d6ec121a425897951900ab692b612a61d6240"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d08d6ec121a425897951900ab692b612a61d6240", "reference": "d08d6ec121a425897951900ab692b612a61d6240", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/event-dispatcher-contracts": "^2", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^4.4|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "time": "2021-02-18T17:12:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/event-dispatcher"}, {"name": "symfony/event-dispatcher-contracts", "version": "v2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "0ba7d54483095a198fa51781bc608d17e84dffa2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/0ba7d54483095a198fa51781bc608d17e84dffa2", "reference": "0ba7d54483095a198fa51781bc608d17e84dffa2", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "time": "2020-09-07T11:33:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/event-dispatcher-contracts"}, {"name": "symfony/finder", "version": "v5.2.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "0d639a0943822626290d169965804f79400e6a04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/0d639a0943822626290d169965804f79400e6a04", "reference": "0d639a0943822626290d169965804f79400e6a04", "shasum": ""}, "require": {"php": ">=7.2.5"}, "time": "2021-02-15T18:55:04+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/finder"}, {"name": "symfony/http-client-contracts", "version": "v2.3.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "41db680a15018f9c1d4b23516059633ce280ca33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/41db680a15018f9c1d4b23516059633ce280ca33", "reference": "41db680a15018f9c1d4b23516059633ce280ca33", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "time": "2020-10-14T17:08:19+00:00", "type": "library", "extra": {"branch-version": "2.3", "branch-alias": {"dev-main": "2.3-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/http-client-contracts"}, {"name": "symfony/http-foundation", "version": "v5.2.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "54499baea7f7418bce7b5ec92770fd0799e8e9bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/54499baea7f7418bce7b5ec92770fd0799e8e9bf", "reference": "54499baea7f7418bce7b5ec92770fd0799e8e9bf", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"predis/predis": "~1.0", "symfony/cache": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0"}, "suggest": {"symfony/mime": "To use the file extension guesser"}, "time": "2021-02-25T17:16:57+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/http-foundation"}, {"name": "symfony/http-kernel", "version": "v5.2.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "b8c63ef63c2364e174c3b3e0ba0bf83455f97f73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/b8c63ef63c2364e174c3b3e0ba0bf83455f97f73", "reference": "b8c63ef63c2364e174c3b3e0ba0bf83455f97f73", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "~1.0", "symfony/deprecation-contracts": "^2.1", "symfony/error-handler": "^4.4|^5.0", "symfony/event-dispatcher": "^5.0", "symfony/http-client-contracts": "^1.1|^2", "symfony/http-foundation": "^4.4|^5.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/browser-kit": "<4.4", "symfony/cache": "<5.0", "symfony/config": "<5.0", "symfony/console": "<4.4", "symfony/dependency-injection": "<5.1.8", "symfony/doctrine-bridge": "<5.0", "symfony/form": "<5.0", "symfony/http-client": "<5.0", "symfony/mailer": "<5.0", "symfony/messenger": "<5.0", "symfony/translation": "<5.0", "symfony/twig-bridge": "<5.0", "symfony/validator": "<5.0", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^4.4|^5.0", "symfony/config": "^5.0", "symfony/console": "^4.4|^5.0", "symfony/css-selector": "^4.4|^5.0", "symfony/dependency-injection": "^5.1.8", "symfony/dom-crawler": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "symfony/routing": "^4.4|^5.0", "symfony/stopwatch": "^4.4|^5.0", "symfony/translation": "^4.4|^5.0", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "time": "2021-03-10T17:07:35+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/http-kernel"}, {"name": "symfony/mime", "version": "v5.2.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "554ba128f1955038b45db5e1fa7e93bfc683b139"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/554ba128f1955038b45db5e1fa7e93bfc683b139", "reference": "554ba128f1955038b45db5e1fa7e93bfc683b139", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.15"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/property-access": "^4.4|^5.1", "symfony/property-info": "^4.4|^5.1", "symfony/serializer": "^5.2"}, "time": "2021-03-07T16:08:20+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/mime"}, {"name": "symfony/polyfill-ctype", "version": "v1.22.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "c6c942b1ac76c82448322025e084cadc56048b4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/c6c942b1ac76c82448322025e084cadc56048b4e", "reference": "c6c942b1ac76c82448322025e084cadc56048b4e", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-ctype": "For best performance"}, "time": "2021-01-07T16:49:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-ctype"}, {"name": "symfony/polyfill-iconv", "version": "v1.22.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "06fb361659649bcfd6a208a0f1fcaf4e827ad342"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/06fb361659649bcfd6a208a0f1fcaf4e827ad342", "reference": "06fb361659649bcfd6a208a0f1fcaf4e827ad342", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-iconv": "For best performance"}, "time": "2021-01-22T09:19:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-iconv"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.22.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "5601e09b69f26c1828b13b6bb87cb07cddba3170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/5601e09b69f26c1828b13b6bb87cb07cddba3170", "reference": "5601e09b69f26c1828b13b6bb87cb07cddba3170", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "time": "2021-01-22T09:19:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-grapheme"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.22.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "2d63434d922daf7da8dd863e7907e67ee3031483"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/2d63434d922daf7da8dd863e7907e67ee3031483", "reference": "2d63434d922daf7da8dd863e7907e67ee3031483", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "time": "2021-01-22T09:19:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-idn"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.22.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "43a0283138253ed1d48d352ab6d0bdb3f809f248"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/43a0283138253ed1d48d352ab6d0bdb3f809f248", "reference": "43a0283138253ed1d48d352ab6d0bdb3f809f248", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "time": "2021-01-22T09:19:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-normalizer"}, {"name": "symfony/polyfill-mbstring", "version": "v1.22.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "5232de97ee3b75b0360528dae24e73db49566ab1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/5232de97ee3b75b0360528dae24e73db49566ab1", "reference": "5232de97ee3b75b0360528dae24e73db49566ab1", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2021-01-22T09:19:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/polyfill-php72", "version": "v1.22.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9", "reference": "cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2021-01-07T16:49:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php72"}, {"name": "symfony/polyfill-php73", "version": "v1.22.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "a678b42e92f86eca04b7fa4c0f6f19d097fb69e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/a678b42e92f86eca04b7fa4c0f6f19d097fb69e2", "reference": "a678b42e92f86eca04b7fa4c0f6f19d097fb69e2", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2021-01-07T16:49:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php73"}, {"name": "symfony/polyfill-php80", "version": "v1.22.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "dc3063ba22c2a1fd2f45ed856374d79114998f91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/dc3063ba22c2a1fd2f45ed856374d79114998f91", "reference": "dc3063ba22c2a1fd2f45ed856374d79114998f91", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2021-01-07T16:49:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php80"}, {"name": "symfony/process", "version": "v5.2.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "313a38f09c77fbcdc1d223e57d368cea76a2fd2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/313a38f09c77fbcdc1d223e57d368cea76a2fd2f", "reference": "313a38f09c77fbcdc1d223e57d368cea76a2fd2f", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.15"}, "time": "2021-01-27T10:15:41+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/process"}, {"name": "symfony/routing", "version": "v5.2.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "cafa138128dfd6ab6be1abf6279169957b34f662"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/cafa138128dfd6ab6be1abf6279169957b34f662", "reference": "cafa138128dfd6ab6be1abf6279169957b34f662", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/config": "<5.0", "symfony/dependency-injection": "<4.4", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "psr/log": "~1.0", "symfony/config": "^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "time": "2021-02-22T15:48:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/routing"}, {"name": "symfony/service-contracts", "version": "v2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "d15da7ba4957ffb8f1747218be9e1a121fd298a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/d15da7ba4957ffb8f1747218be9e1a121fd298a1", "reference": "d15da7ba4957ffb8f1747218be9e1a121fd298a1", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.0"}, "suggest": {"symfony/service-implementation": ""}, "time": "2020-09-07T11:33:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/service-contracts"}, {"name": "symfony/string", "version": "v5.2.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "4e78d7d47061fa183639927ec40d607973699609"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/4e78d7d47061fa183639927ec40d607973699609", "reference": "4e78d7d47061fa183639927ec40d607973699609", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0", "symfony/http-client": "^4.4|^5.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0"}, "time": "2021-02-16T10:20:28+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\String\\": ""}, "files": ["Resources/functions.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/string"}, {"name": "symfony/translation", "version": "v5.2.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "0947ab1e3aabd22a6bef393874b2555d2bb976da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/0947ab1e3aabd22a6bef393874b2555d2bb976da", "reference": "0947ab1e3aabd22a6bef393874b2555d2bb976da", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.15", "symfony/translation-contracts": "^2.3"}, "conflict": {"symfony/config": "<4.4", "symfony/dependency-injection": "<5.0", "symfony/http-kernel": "<5.0", "symfony/twig-bundle": "<5.0", "symfony/yaml": "<4.4"}, "provide": {"symfony/translation-implementation": "2.3"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^4.4|^5.0", "symfony/console": "^4.4|^5.0", "symfony/dependency-injection": "^5.0", "symfony/finder": "^4.4|^5.0", "symfony/http-kernel": "^5.0", "symfony/intl": "^4.4|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^4.4|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "time": "2021-03-06T07:59:01+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation"}, {"name": "symfony/translation-contracts", "version": "v2.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "e2eaa60b558f26a4b0354e1bbb25636efaaad105"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/e2eaa60b558f26a4b0354e1bbb25636efaaad105", "reference": "e2eaa60b558f26a4b0354e1bbb25636efaaad105", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "time": "2020-09-28T13:05:58+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation-contracts"}, {"name": "symfony/var-dumper", "version": "v5.2.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "002ab5a36702adf0c9a11e6d8836623253e9045e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/002ab5a36702adf0c9a11e6d8836623253e9045e", "reference": "002ab5a36702adf0c9a11e6d8836623253e9045e", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.15"}, "conflict": {"phpunit/phpunit": "<5.4.3", "symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "time": "2021-03-06T07:59:01+00:00", "bin": ["Resources/bin/var-dump-server"], "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/var-dumper"}, {"name": "theseer/tokenizer", "version": "1.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "75a63c33a8577608444246075ea0af0d052e452a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/75a63c33a8577608444246075ea0af0d052e452a", "reference": "75a63c33a8577608444246075ea0af0d052e452a", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "time": "2020-07-12T23:59:07+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "funding": [{"url": "https://github.com/theseer", "type": "github"}], "install-path": "../theseer/tokenizer"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "2.2.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "b43b05cf43c1b6d849478965062b6ef73e223bb5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/b43b05cf43c1b6d849478965062b6ef73e223bb5", "reference": "b43b05cf43c1b6d849478965062b6ef73e223bb5", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5"}, "time": "2020-07-13T06:12:54+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "install-path": "../tijsverkoyen/css-to-inline-styles"}, {"name": "twilio/sdk", "version": "6.20.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/twilio/twilio-php.git", "reference": "7494b78da4f5af5b761dd7b4fd9bbe52bd746f55"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twilio/twilio-php/zipball/7494b78da4f5af5b761dd7b4fd9bbe52bd746f55", "reference": "7494b78da4f5af5b761dd7b4fd9bbe52bd746f55", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.3 || ^7.0", "phpunit/phpunit": ">=4.5", "theseer/phpdox": "^0.12.0"}, "suggest": {"guzzlehttp/guzzle": "An HTTP client to execute the API requests"}, "time": "2021-03-24T21:42:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Twilio\\": "src/<PERSON>wi<PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Twilio API Team", "email": "<EMAIL>"}], "description": "A PHP wrapper for Twilio's API", "homepage": "http://github.com/twilio/twilio-php", "keywords": ["api", "sms", "twi<PERSON>"], "install-path": "../twilio/sdk"}, {"name": "vlucas/phpdotenv", "version": "v4.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "da64796370fc4eb03cc277088f6fede9fde88482"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/da64796370fc4eb03cc277088f6fede9fde88482", "reference": "da64796370fc4eb03cc277088f6fede9fde88482", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.7.3", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}, "time": "2021-01-20T15:11:48+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gjcampbell.co.uk/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://vancelucas.com/"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "install-path": "../vlucas/phpdotenv"}, {"name": "voku/portable-ascii", "version": "1.5.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "80953678b19901e5165c56752d087fc11526017c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/80953678b19901e5165c56752d087fc11526017c", "reference": "80953678b19901e5165c56752d087fc11526017c", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "time": "2020-11-12T00:07:28+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "install-path": "../voku/portable-ascii"}, {"name": "webmozart/assert", "version": "1.10.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/6964c76c7804814a842473e0c8fd15bab0f18e25", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "time": "2021-03-09T10:59:23+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "install-path": "../webmozart/assert"}], "dev": true, "dev-package-names": ["doctrine/instantiator", "facade/flare-client-php", "facade/ignition", "facade/ignition-contracts", "filp/whoops", "fzaninotto/faker", "hamcrest/hamcrest-php", "mockery/mockery", "myclabs/deep-copy", "nunomaduro/collision", "phar-io/manifest", "phar-io/version", "phpdocumentor/reflection-common", "phpdocumentor/reflection-docblock", "phpdocumentor/type-resolver", "phpspec/prophecy", "phpunit/php-code-coverage", "phpunit/php-file-iterator", "phpunit/php-text-template", "phpunit/php-timer", "phpunit/php-token-stream", "phpunit/phpunit", "sebastian/code-unit-reverse-lookup", "sebastian/comparator", "sebastian/diff", "sebastian/environment", "sebastian/exporter", "sebastian/global-state", "sebastian/object-enumerator", "sebastian/object-reflector", "sebastian/recursion-context", "sebastian/resource-operations", "sebastian/type", "sebastian/version", "theseer/tokenizer", "webmozart/assert"]}
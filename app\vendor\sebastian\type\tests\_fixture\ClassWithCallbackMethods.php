<?php declare(strict_types=1);
/*
 * This file is part of sebastian/type.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace <PERSON>\Type\TestFixture;

final class ClassWithCallbackMethods
{
    public static function staticCallback(): void
    {
    }

    public function nonStaticCallback(): void
    {
    }
}

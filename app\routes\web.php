<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// forca todos os ids informados serem numeros
Route::pattern('id', '[0-9]+');
// forca todos serem letras minusculas
Route::pattern('codigo', '[a-z\-]+');


Route::get('/', 'HomeController@index')->name('home');
// Route::get('/teste', '<PERSON>ush<PERSON><PERSON>roller@teste')->name('teste');


Route::get('/admin/login', 'Admin<PERSON><PERSON>roller@login')->name('login');
Route::post('/admin/login', 'AdminController@verificalogin')->name('verificalogin');
Route::get('/admin/sair', 'AdminController@logout')->name('sair');
Route::post('/admin/lembrarsenha', 'AdminController@lembrarsenha')->name('lembrarsenha');

Route::post('/login', 'LoginController@verificalogin')->name('verificalogin_app');
Route::get('/sair', 'LoginController@logout')->name('sair_app');
Route::get('/novasenha/{tipousuario}', 'LoginController@novasenhaform')->name('novasenhaform_app');
Route::post('/novasenha', 'LoginController@novasenha')->name('novasenha_app');
Route::post('/novasenhaenviado', 'LoginController@novasenhaenviado')->name('novasenhaenviado_app');
Route::post('/atualizarcelular', 'LoginController@atualizarcelular')->name('atualizarcelular_app');

Route::get('/login_auto', 'LoginController@loginOneSignal')->name('verificalogin_app_auto');

Route::get('/ativar', 'HomeController@ativar')->name('ativar_app');
Route::post('/cadastro', 'HomeController@cadastro')->name('cadastro_app');
Route::post('/cidade', 'HomeController@index')->name('escolhaunidade_app');
Route::get('/cidade/{cidade}', 'HomeController@index')->name('cidade');
Route::get('/promocoes/{cidade}', 'HomeController@promocoes')->name('promocoes');


// Paginas principais
Route::get('/consumidores/{id?}', 'HomeController@consumidores')->name('consumidores_app');
Route::post('/consumidores/premios', 'HomeController@solicitarpremio')->name('consumidores_app-solicitarpremio');
Route::post('/consumidores/mudarcadastro', 'HomeController@mudarcadastro')->name('consumidores_app-mudarcadastro');
Route::post('/consumidores/avaliacao', 'HomeController@avaliacao')->name('consumidores_app-avaliacao');

Route::get('/Abastecer', 'AbastecerController@index')->name('abastecer');

Route::get('/crm/cron', 'CRMController@cron')->name('crm-cron');

Route::group(['middleware' => ['verificaLogado']], function () {
  Route::get('/calculadora', 'HomeController@calculadora')->name('calculadora');
  Route::get('admin', 'AdminController@index')->name('admin');
  //Route::get('admin', 'AdminController@dashboard')->name('admin');
  Route::get('admin/pontosResgatados', 'AdminController@pontosResgatados')->name('admin-pontos-resgatados');
  Route::post('admin', 'AdminController@dashboard')->name('admin-dashboard');

  // gestores
  Route::get('admin/gestores/{id?}', 'GestoresController@index')->name('gestores');
  Route::post('admin/gestores/salvar', 'GestoresController@salvar')->name('gestores-salvar');
  Route::get('admin/gestores/apagar/{id}', 'GestoresController@apagar')->name('gestores-apagar');
  Route::get('admin/gestores/atualizar/{id}/{campo}', 'GestoresController@atualizar')->name('gestores-atualizar');
  Route::post('admin/gestores/filtrar', 'GestoresController@filtrar')->name('gestores-filtrar');

  // pontos
  Route::get('admin/pontos/{id?}', 'PontosController@index')->name('pontos');
  Route::post('admin/pontos', 'PontosController@index')->name('pontos-filtrar');
  Route::post('admin/pontos/salvar', 'PontosController@salvar')->name('pontos-salvar');
  Route::get('admin/pontos/apagar/{id}', 'PontosController@apagar')->name('pontos-apagar');
  Route::get('admin/pontos/atualizar/{id}/{campo}', 'PontosController@atualizar')->name('pontos-atualizar');
  Route::get('admin/pontos/notificar/{id}', 'PontosController@notificar')->name('pontos-notificar');
  Route::post('admin/pontos/exportar', 'PontosController@exportar')->name('pontos-exportar');
  Route::any('admin/pontos/adicionar', 'PontosController@adicionar')->name('pontos-adicionar');
  Route::get('admin/pontos/notificar/{id}', 'PontosController@notificar')->name('pontos-notificar');
  Route::any('admin/pontos/baixar', 'PontosController@baixar')->name('pontos-baixar');

  // consumidores
  Route::get('admin/consumidores/{id?}', 'ConsumidoresController@index')->name('consumidores');
  Route::post('admin/consumidores', 'ConsumidoresController@index')->name('consumidores-filtrar');
  Route::post('admin/consumidores/salvar', 'ConsumidoresController@salvar')->name('consumidores-salvar');
  Route::get('admin/consumidores/apagar/{id}', 'ConsumidoresController@apagar')->name('consumidores-apagar');
  Route::get('admin/consumidores/atualizar/{id}/{campo}', 'ConsumidoresController@atualizar')->name('consumidores-atualizar');
  Route::post('admin/consumidores/imagem', 'ConsumidoresController@imagem')->name('consumidores-imagem');
  Route::get('admin/consumidores/imagemexcluir/{id}', 'ConsumidoresController@imagemexcluir')->name('consumidores-imagem-excluir');
  Route::get('admin/consumidores/notificar/{id}', 'ConsumidoresController@notificar')->name('consumidores-notificar');
  Route::post('admin/consumidores/exportar', 'ConsumidoresController@exportar')->name('consumidores-exportar');
  Route::get('admin/consumidores/notificar/{id}', 'ConsumidoresController@notificar')->name('consumidores-notificar');

  // clientes
  Route::get('admin/clientes/{id?}', 'ClientesController@index')->name('clientes');
  Route::post('admin/clientes', 'ClientesController@index')->name('clientes-filtrar');
  Route::post('admin/clientes/salvar', 'ClientesController@salvar')->name('clientes-salvar');
  Route::get('admin/clientes/apagar/{id}', 'ClientesController@apagar')->name('clientes-apagar');
  Route::get('admin/clientes/atualizar/{id}/{campo}', 'ClientesController@atualizar')->name('clientes-atualizar');
  Route::post('admin/clientes/imagem', 'ClientesController@imagem')->name('clientes-imagem');
  Route::get('admin/clientes/imagemexcluir/{id}', 'ClientesController@imagemexcluir')->name('clientes-imagem-excluir');

  // premios
  Route::get('admin/premios/{id?}', 'PremiosController@index')->name('premios');
  Route::post('admin/premios', 'PremiosController@index')->name('premios-filtrar');
  Route::post('admin/premios/salvar', 'PremiosController@salvar')->name('premios-salvar');
  Route::get('admin/premios/apagar/{id}', 'PremiosController@apagar')->name('premios-apagar');
  Route::get('admin/premios/atualizar/{id}/{campo}', 'PremiosController@atualizar')->name('premios-atualizar');
  Route::post('admin/premios/imagem', 'PremiosController@imagem')->name('premios-imagem');
  Route::get('admin/premios/imagemexcluir/{id}', 'PremiosController@imagemexcluir')->name('premios-imagem-excluir');

  // acessos
  Route::get('admin/acessos', 'AcessosController@index')->name('acessos');
  Route::post('admin/acessos', 'AcessosController@index')->name('acessos-filtrar');

  // nps
  Route::get('admin/nps', 'NpsController@index')->name('nps');
  Route::post('admin/nps', 'NpsController@index')->name('nps-filtrar');
  Route::get('admin/nps/apagar/{id}', 'NpsController@apagar')->name('nps-apagar');

  // vendedores
  Route::get('admin/vendedores/{id?}', 'VendedoresController@index')->name('vendedores');
  Route::post('admin/vendedores', 'VendedoresController@index')->name('vendedores-filtrar');
  Route::post('admin/vendedores/salvar', 'VendedoresController@salvar')->name('vendedores-salvar');
  Route::get('admin/vendedores/apagar/{id}', 'VendedoresController@apagar')->name('vendedores-apagar');
  Route::get('admin/vendedores/atualizar/{id}/{campo}', 'VendedoresController@atualizar')->name('vendedores-atualizar');

  // unidades
  Route::get('admin/unidades/{id?}', 'UnidadesController@index')->name('unidades');
  Route::post('admin/unidades', 'UnidadesController@index')->name('unidades-filtrar');
  Route::post('admin/unidades/salvar', 'UnidadesController@salvar')->name('unidades-salvar');
  Route::get('admin/unidades/apagar/{id}', 'UnidadesController@apagar')->name('unidades-apagar');
  Route::get('admin/unidades/atualizar/{id}/{campo}', 'UnidadesController@atualizar')->name('unidades-atualizar');

  // produtos
  Route::get('admin/produtos', 'ProdutosController@index')->name('produtos');
  Route::post('admin/produtos/salvar', 'ProdutosController@salvar')->name('produtos-salvar');
  Route::post('admin/produtos', 'ProdutosController@index')->name('produtos-filtrar');
  Route::get('admin/produtos/apagar/{id}', 'ProdutosController@apagar')->name('produtos-apagar');
  Route::get('admin/produtos/{id?}', 'ProdutosController@index')->name('produtos-editar');

  // bicos
  Route::get('admin/bicos', 'BicoController@index')->name('bicos');
  Route::post('admin/bicos/salvar', 'BicoController@salvar')->name('bicos-salvar');
  Route::post('admin/bicos', 'BicoController@index')->name('bicos-filtrar');
  Route::get('admin/bicos/apagar/{id}', 'BicoController@apagar')->name('bicos-apagar');
  Route::get('admin/bicos/{id?}', 'BicoController@index')->name('bicos-editar');

  // propagandas
    Route::get('admin/cadastroporatendente', 'AdminController@cadastroporatendente')->name('cadastroporatendente');
  Route::get('admin/propagandas/{id?}', 'PropagandasController@index')->name('propagandas');
  Route::post('admin/propagandas', 'PropagandasController@index')->name('propagandas-filtrar');
  Route::post('admin/propagandas/salvar', 'PropagandasController@salvar')->name('propagandas-salvar');
  Route::get('admin/propagandas/apagar/{id}', 'PropagandasController@apagar')->name('propagandas-apagar');
  Route::post('admin/propagandas/imagem', 'PropagandasController@imagem')->name('propagandas-imagem');
  Route::get('admin/propagandas/imagemexcluir/{id}', 'PropagandasController@imagemexcluir')->name('propagandas-imagem-excluir');

  // promocoes
  Route::get('admin/promocoes/{id?}', 'PromocoesController@index')->name('promocoes');
  Route::post('admin/promocoes', 'PromocoesController@index')->name('promocoes-filtrar');
  Route::post('admin/promocoes/salvar', 'PromocoesController@salvar')->name('promocoes-salvar');
  Route::get('admin/promocoes/apagar/{id}', 'PromocoesController@apagar')->name('promocoes-apagar');
  Route::post('admin/promocoes/imagem', 'PromocoesController@imagem')->name('promocoes-imagem');
  Route::get('admin/promocoes/imagemexcluir/{id}', 'PromocoesController@imagemexcluir')->name('promocoes-imagem-excluir');

  // grupos
  Route::get('admin/grupos/{id?}', 'GruposController@index')->name('grupos');
  Route::post('admin/grupos', 'GruposController@index')->name('grupos-filtrar');
  Route::post('admin/grupos/salvar', 'GruposController@salvar')->name('grupos-salvar');
  Route::get('admin/grupos/apagar/{id}', 'GruposController@apagar')->name('grupos-apagar');

  // categorias
  Route::get('admin/categorias/{id?}', 'CategoriasController@index')->name('categorias');
  Route::post('admin/categorias', 'CategoriasController@index')->name('categorias-filtrar');
  Route::post('admin/categorias/salvar', 'CategoriasController@salvar')->name('categorias-salvar');
  Route::get('admin/categorias/apagar/{id}', 'CategoriasController@apagar')->name('categorias-apagar');
  Route::post('admin/categorias/imagem', 'CategoriasController@imagem')->name('categorias-imagem');
  Route::get('admin/categorias/imagemexcluir/{id}', 'CategoriasController@imagemexcluir')->name('categorias-imagem-excluir');

  // premioscategorias
  Route::get('admin/premioscategorias/{id?}', 'PremiosCategoriasController@index')->name('premioscategorias');
  Route::post('admin/premioscategorias', 'PremiosCategoriasController@index')->name('premioscategorias-filtrar');
  Route::post('admin/premioscategorias/salvar', 'PremiosCategoriasController@salvar')->name('premioscategorias-salvar');
  Route::get('admin/premioscategorias/apagar/{id}', 'PremiosCategoriasController@apagar')->name('premioscategorias-apagar');

  // notificacoes
  Route::get('admin/notificacoes', 'NotificacoesController@index')->name('notificacoes');
  Route::post('admin/notificacoes', 'NotificacoesController@index')->name('notificacoes-filtrar');
  Route::get('admin/notificacoes/apagar/{id}', 'NotificacoesController@apagar')->name('notificacoes-apagar');
  Route::get('admin/notificacoes/visualizar/{id}', 'NotificacoesController@visualizar')->name('notificacoes-visualizar');
  Route::get('admin/notificacoes/enviar/{id?}', 'NotificacoesController@enviar')->name('notificacoes-enviar');

  // Rotas para envio de notificações push
  Route::get('admin/notificacoes/enviar-push', 'NotificacoesController@enviarNotificacaoForm')->name('notificacoes.enviar.form');
  Route::post('admin/notificacoes/enviar-push', 'NotificacoesController@enviarNotificacao')->name('notificacoes.enviar.push');

  Route::get('admin/pontuacaoporatendente', 'AdminController@pontuacaoporatendente')->name('pontuacaoporatendente');

  // CRM
  Route::get('admin/crm', 'CRMController@index')->name('crm');
  Route::post('admin/crm', 'CRMController@index')->name('crm-filtrar');
  Route::post('admin/crm/sendMessage', 'CRMController@SendMessageWhatsApp');

  //Comunicação
  Route::get('admin/comunicacao', 'ComunicacaoController@index')->name('comunicacao');
  Route::post('admin/whatsconnect', 'ComunicacaoController@ConectarWhatsApp')->name('wpconnect');
  Route::get('admin/statusconnection', 'ComunicacaoController@CheckStatusConnection');
  Route::get('admin/closeConnection', 'ComunicacaoController@SessionClose')->name('wpconnectclose');
  Route::post('admin/sendMessage', 'ComunicacaoController@SendMessageText')->name('send-message');
  Route::get('admin/filterconsumer', 'ComunicacaoController@FilterConsumer');
  Route::get('admin/mensagem', 'MensagemController@index')->name('mensagens');
  Route::post('admin/mensagem/save', 'MensagemController@salvar')->name('mensagens-save');
  Route::get('admin/mensagem/clearImage', 'MensagemController@ClearImagemPath')->name('mensagens-clear-image');

  Route::get('admin/relatorios/pontosaresgatar', 'RelatoriosController@PontosAResgatar')->name('pontos-a-resgatar');
  Route::post('admin/relatorios/pontosaresgatar', 'RelatoriosController@PontosAResgatar')->name('pontos-a-resgatar');
  Route::get('admin/message/sendMessagePontosResgatar', 'RelatoriosController@SendMessagePontosResgatar');
  Route::get('admin/ResumoPorCombustivel', 'AdminController@ResumoPorCombustivel');

    Route::get('admin/ResumoPorConsumidor', 'AdminController@ResumoPorConsumidor')->name('resumo-por-consumidor');
    Route::post('admin/ResumoPorConsumidor', 'AdminController@ResumoPorConsumidor')->name('resumo-por-consumidor');
    Route::get('admin/VendasPorBico', 'AdminController@VendaPorBico')->name('rel-vendas-bico');
    Route::get('admin/VendasPorBico/Detalhe', 'AdminController@VendaPorBicoDetalhe')->name('rel-vendas-bico-detalhe');
    Route::post('admin/VendasPorBico', 'AdminController@VendaPorBico')->name('rel-vendas-bico');
});

// Api Connect
Route::get('/Abastecer/InfoQrCode', 'AbastecerController@QrCodeInfo');
Route::get('/Abastecer/Reserve', 'AbastecerController@Reserve');
Route::get('/Abastecer/Authorize', 'AbastecerController@AuthorizeFuelPointNumber');
Route::get('/Abastecer/Status', 'AbastecerController@GetStatus');

Route::get('/{codigo}', 'HomeController@cliente')->name('cliente_app');
Route::get('/{codigo}/cadastrar', 'HomeController@cadastrar')->name('cadastrar_app');

Route::get('/integration/dfranquias', 'IntegrationController@executeDFranquias')->name('integration-dfranquias');

// COMUNICAÇÃO

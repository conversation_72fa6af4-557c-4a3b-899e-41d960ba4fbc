# sebastian/environment

This component provides functionality that helps writing PHP code that has runtime-specific (PHP / HHVM) execution paths.

[![Latest Stable Version](https://img.shields.io/packagist/v/sebastian/environment.svg?style=flat-square)](https://packagist.org/packages/sebastian/environment)
[![Minimum PHP Version](https://img.shields.io/badge/php-%3E%3D%207.1-8892BF.svg?style=flat-square)](https://php.net/)
[![Build Status](https://travis-ci.org/sebastianbergmann/environment.svg?branch=master)](https://travis-ci.org/sebastianbergmann/environment)

## Installation

You can add this library as a local, per-project dependency to your project using [Composer](https://getcomposer.org/):

    composer require sebastian/environment

If you only need this library during development, for instance to run your project's test suite, then you should add it as a development-time dependency:

    composer require --dev sebastian/environment

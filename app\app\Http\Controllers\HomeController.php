<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Http\Controllers\Notificacao;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

use App\Clientes;
use App\Consumidores;
use App\Cadastros;
use App\Pontos;
use App\Premios;
use App\Acessos;
use App\Notificacoes;
use App\Propagandas;
use App\Vendedores;
use App\Promocoes;
use App\Categorias;
use App\Unidades;
use App\Nps;

class HomeController extends Controller
{

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $cidade = null)
    {

        if($request->session()->has('logadoHome')) {
            return redirect()->route($request->session()->get('logadoHome').'_app');
        }

        if($request->has('cidade') and $request->get('cidade') != '') {
            return redirect()->route('cidade', ['cidade'=>$request->get('cidade')]);
        }

        $clientes = Clientes::where('ativo','=','1')->orderBy('nome','ASC');
        $promocoes = array();
        if($cidade != null) {
            $clientes->where('cidade','=',$cidade);
            $promocoes = Promocoes::select('promocoes.*','clientes.nome', 'categorias.nome AS categoria')
                                ->join('clientes','clientes.id','=','promocoes.clientes_id')
                                ->join('categorias','categorias.id','=','promocoes.categorias_id')
                                ->where('clientes.cidade','=',$cidade)
                                ->whereRaw('dtinicio <= CURDATE() AND dtfim >= CURDATE()')
                                ->get();
        }
        $clientes = $clientes->get();

        $params = array(
            'clientes' => $clientes,
            'cidades'  => Clientes::select('cidade','estado')->where('ativo','=','1')->orderBy('cidade','ASC')->groupBy('cidade')->get(),
            'categorias'  => Categorias::orderBy('nome','ASC')->get(),
            'cidade' => $cidade,
            'promocoes' => $promocoes,
            'template' => 'clientes'
        );



        return view('home')->with($params);
    }



    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function cliente($codigo, Request $request)
    {

        if($request->session()->has('logadoHome')) {
            return redirect()->route($request->session()->get('logadoHome').'_app');
        }

        $cliente = Clientes::where('url','=',$codigo)->first();
        if(empty($cliente)) {
            return redirect()->route('home')->withErrors(array('error' => 'Empresa não encontrada.'));
        }

        $propagandas = Propagandas::where('clientes_id','=',$cliente->id)->whereRaw('dtinicio <= CURDATE() AND dtfim >= CURDATE()')->get();

        $params = array(
            'cliente' => $cliente,
            'propagandas' => $propagandas,
        );

        return view('login')->with($params);
    }

    /**
     * Consumidor
     *
     * @return Response
     */
    public function consumidores(Request $request, $id = null)
    {


        if($request->has('token')) {
            $consumidor = Consumidores::where('token','=',$request->get('token'))->first();
            if(!empty($consumidor)) {
                $request->session()->forget('logadoHome');
                $request->session()->forget('logadoHome_usuario');
                $request->session()->put('logadoHome', 'consumidores');
                $request->session()->put('logadoHome_usuario', $consumidor);
                Acessos::salvar(array('consumidores_id'=>$consumidor->id));
            }
        }

        if(!$request->session()->has('logadoHome') or $request->session()->get('logadoHome') != 'consumidores') {
            return redirect()->route('home')->withErrors(array('error' => __('auth.failed')));
        }



        $usuario = $request->session()->get('logadoHome_usuario');

        $pontuacao = $usuario->pontuacao($usuario->cliente->id);
        $compras = $usuario->pontos($usuario->cliente->id)->where('premios_id','=',NULL)->count();

        $pontuacoes = Pontos::select('pontos.*','clientes.nome AS cliente')
        ->join('clientes','pontos.clientes_id','=','clientes.id')
        ->where('status','=',1)
        ->where('clientes_id','=',$usuario->cliente->id)
        ->where('consumidores_id','=',$usuario->id)
        ->orderBy('created_at','DESC')
        ->get();

        $premios = Premios::select('premios.*', 'premioscategorias.nome AS categoria')
        ->leftjoin('premioscategorias','premioscategorias.id','=','premios.premioscategorias_id')
        ->where('premios.status','=',1)
        ->where('premios.clientes_id','=',$usuario->cliente->id)
        ->orderBy('pontos','ASC')
        ->get();

        $premio = null;
        if($id) {
            $premio = Premios::select('premios.*')
            ->where('id','=',$id)
            ->where('status','=',1)
            ->where('clientes_id','=',$usuario->cliente->id)
            ->firstOrFail();

            if($premio->grupos_id) {
                $clientes = array();
                foreach(json_decode($premio->grupo->clientes) as $clienteid) {
                    $clientes[] = Clientes::find($clienteid);
                }
                $premio->clientes = $clientes;
            }

        }

        $propagandas = Propagandas::where('clientes_id','=',$usuario->cliente->id)->whereRaw('dtinicio <= CURDATE() AND dtfim >= CURDATE()')->get();
        $vendedor = Vendedores::where('clientes_id','=',$usuario->cliente->id)->where('email','=',$usuario->email)->where('status','=',1)->first();
        if($usuario->email == null) {
            $vendedor = null;
        }

        $params = array(
            'tituloPagina' => 'Painel',
            'usuario' => $request->session()->get('logadoHome_usuario'),
            'pontuacao' => $pontuacao,
            'compras' => $compras,
            'pontuacoes' => $pontuacoes,
            'premios' => $premios,
            'propagandas' => $propagandas,
            'premio' => $premio,
            'vendedor' => $vendedor,
            'cliente' => $usuario->cliente
        );

        return view('consumidores')->with($params);



    }

    public function cadastrar($codigo, Request $request)
    {


        $cliente = Clientes::where('url','=',$codigo)->first();
        if(empty($cliente)) {
            return redirect()->route('home')->withErrors(array('error' => 'Empresa não encontrada.'));
        }

        $propagandas = Propagandas::where('clientes_id','=',$cliente->id)
        ->whereRaw('dtinicio <= CURDATE() AND dtfim >= CURDATE()')->get();

        $vendedores = Vendedores::where('clientes_id', $cliente->id)->orderby('nome')->get();

        $unidades = Unidades::where('clientes_id', $cliente->id)
        ->where(function($query) {
            $query->where('usa_fidelidade', true)
                  ->orWhereNull('usa_fidelidade');
        })
        ->orderBy('nome')->get();

        $params = array(
            'cliente' => $cliente,
            'tituloPagina' => 'Cadastre-se',
            'propagandas' => $propagandas,
            'vendedores' => $vendedores,
            'unidades' => $unidades
        );

        return view('cadastrar')->with($params);
    }

    /**
     * Inserir novo registro
     *
     * @return Response
     */
    public function cadastro(Request $request)
    {

        $dados = $request->all();
        //var_dump($dados); exit;

        $dados['senha'] = Hash::make($request->get('senha'));
        $dados['ativo'] = 1; // já ativo pq email é opcional

        $cliente = Clientes::where('url','=',$dados['url'])->first();

        // verificar CPF ou CNPJ
        $consumidorexiste = Consumidores::where('cadastronacional','=',$dados['cadastronacional'])->first();
        if(!empty($consumidorexiste)) {
            $dados['id'] = $consumidorexiste->id;
        }

        $atendenteId = "";
        $unidadeId   = "";

        if (!empty($dados['atendente_id'])){
            $atendenteId = $dados['atendente_id'];
        }

        if (!empty($dados['unidade_id'])){
            $unidadeId = $dados['unidade_id'];
        }

        // verificar se Email existe
        $emailexiste = Consumidores::where('email','=',$dados['email'])->where('cadastronacional','!=',$dados['cadastronacional'])->first();
        if(!empty($emailexiste)) {
            return back()->withErrors(array('error'=>'Email já tem cadastro em outro CPF/CNPJ. Use opção "lembrar senha.'));
        }

        // Se já existir um atendente para o consumidor, desprezar o campo atendente_id
        if (!empty($consumidorexiste->atendente_id)){
            unset($dados['atendente_id']);
        }

        // Se já existir uma unidade_id para o consumidor, desprezar o campo unidade_id
        if (!empty($consumidorexiste->unidade_id)){
            unset($dados['unidade_id']);
        }

        //$dados['nome'] = $dados['nome'].' '.$dados['sobrenome'];
        //$dados['nome'] = $dados['nome'];
        $salvo = Consumidores::salvar($dados);

		// verifica se deu erro
		if ($salvo instanceof \Illuminate\Support\MessageBag) {
			return back()->withErrors(array('error'=>$salvo->getMessageBag()->first()));
		}

        $diasvalidade = $cliente->diasvalidade ? $cliente->diasvalidade : 365;
        $hoje = new \Carbon\Carbon('now');
        $dtvalidade = $hoje->addDay($diasvalidade);

        $existeCadastro = Cadastros::where("clientes_id", $cliente->id)->where("consumidores_id", $salvo->id)->get()->first();

		if (intval($cliente->pontos_cadastro) > 0 && empty($existeCadastro->id)){ // lança pontuação somente se consumidor não estiver pre cadastrado

            $dados_pontos = array(
                'clientes_id' => $cliente->id,
                'consumidores_id' => $salvo->id,
                'vendedores_id' => $atendenteId,
                'unidades_id' => $unidadeId,
                'valor' => 0,
                'dtcompra' => date('Y-m-d'),
                'pontuacao' => $cliente->pontos_cadastro,
                'validade' => $dtvalidade->format('d/m/Y'),
                'status' => 1
            );

            $ponto = Pontos::salvar($dados_pontos);

            // NOTIFICAR
            $dados_notificacao = array(
                'tipo' => 'pontonovo',
                'template' => 'pontonovo',
                'variaveis' => serialize($dados_pontos),
                'email' => $dados['email'],
                'celular' => $dados['celular'],
                'nome' => $dados['nome'],
                'assunto' => 'Você ganhou novos pontos',
                'canais' => 'email',
                'prioridade' => 2,
                'status' => 0
            );

            $notificacao = Notificacoes::salvar($dados_notificacao);
        }

        //gera token
        if($salvo->token == '') {
            $salvo->token = Hash::make($salvo->id);
            $salvo->save();
        }

        // vincular consumidor ao cliente
        $cadastroexiste = Cadastros::where('clientes_id','=',$cliente->id)->where('consumidores_id','=', $salvo->id)->first();
        if(empty($cadastroexiste)) {
            $novocadastro = Cadastros::salvar(array('unidades_id' => $unidadeId, 'vendedores_id' => $atendenteId, 'clientes_id'=>$cliente->id, 'consumidores_id'=>$salvo->id));
        }

        return redirect()->route('cliente_app',['codigo'=>$cliente->url])->withErrors(array('success' => 'Cadastro realizado com sucesso!'));
    }



    public function ativar(Request $request)
    {

        $consumidor = Consumidores::where('token','=',$request->get('token'))->firstOrFail();

        $consumidor->ativo = 1;
        $consumidor->save();

        return redirect()->route('home')->withErrors(array('success' => 'Ativado com sucesso!'));

    }



    public function solicitarpremio(Request $request)
    {

        $consumidor = $request->session()->get('logadoHome_usuario');
        $premio = Premios::find($request->get('premios_id'));

        // Verificar regras
        if($premio->resgates) {
            $resgates = Pontos::where('premios_id','=',$premio->id)->where('status','=',1)->count();
            if($resgates >= $premio->resgates) {
                return back()->withErrors(array('error'=>'Este prêmio atingiu o limite máximo de resgates.'));
            }
        }
        if($premio->cotamensal) {
            $resgates = Pontos::where('premios_id','=',$premio->id)
                            ->where('status','=',1)
                            ->whereRaw('MONTH(CURDATE()) = MONTH(dtcompra) AND YEAR(CURDATE()) = YEAR(dtcompra)')
                            ->count();
            if($resgates >= $premio->cotamensal) {
                return back()->withErrors(array('error'=>'Este prêmio atingiu o limite máximo de resgate neste mês.'));
            }
        }

        if($consumidor->pontuacao($premio->clientes_id) < $premio->pontos) {
            return back()->withErrors(array('error'=>'Você não tem saldo para resgatar este prêmio.'));
        }


        // Gerar Voucher
        list($segundos, $microssegundos) = explode(' ', microtime());
        $voucher = $microssegundos;

        $dados_pontos = array(
            'clientes_id' => $premio->clientes_id,
            'consumidores_id' => $consumidor->id,
            'premios_id' => $premio->id,
            'valor' => 0,
            'dtcompra' => date('d/m/Y'),
            'pontuacao' => $premio->pontos * -1,
            'validade' => NULL,
            'status' => 1,
            'voucher' => $voucher
        );

        if($premio->sorteio) {
            $dados_pontos['dtresgate'] = date('d/m/Y');
        }

        $ponto = Pontos::salvar($dados_pontos);

        // verifica se deu erro
        if ($ponto instanceof \Illuminate\Support\MessageBag) {
            return back()->with(array('registro'=>$dados_pontos))->withErrors(array('error'=>$ponto->getMessageBag()->first()));
        }

        // Retirar validade de pontos recebidos por terem sidos usados
        $pontosavencer = Pontos::select('pontos.*')
        ->where('status','=',1)
        ->whereRaw('(validade >= CURDATE())')
        ->where('clientes_id','=',$premio->clientes_id)
        ->where('consumidores_id','=',$consumidor->id)
        ->orderBy('validade','ASC')
        ->get();

        $pontosusados = $premio->pontos;
        foreach($pontosavencer as $ponto) {
            $ponto->validade = null;
            $ponto->save();
            if($pontosusados <= $ponto->pontuacao) {
                break;
            }
            $pontosusados -= $ponto->pontuacao;
        }

        // NOTIFICAR
        $dados_notificacao = array(
            'tipo' => 'premioresgate',
            'template' => 'premioresgate',
            'variaveis' => serialize($dados_pontos),
            'email' => $consumidor->email,
            'celular' => $consumidor->celular,
            'nome' => $consumidor->nome,
            'assunto' => 'Você resgatou prêmio',
            'canais' => 'email',
            'prioridade' => 2,
            'status' => 0
        );

        $notificacao = Notificacoes::salvar($dados_notificacao);

        return redirect()->route('home')->withErrors(array('success' => 'Resgatado com sucesso!'));

    }



    public function mudarcadastro(Request $request)
    {

        $clienteid = $request->get('clienteid');
        $cadastro = Cadastros::where('clientes_id','=',$clienteid)->first();

        $cliente = Clientes::where('id','=',$cadastro->clientes_id)->first();

        $usuario = $request->session()->get('logadoHome_usuario');
        $usuario->cliente = $cliente;

        $request->session()->forget('logadoHome_usuario');
        $request->session()->put('logadoHome_usuario', $usuario);

        return redirect()->route('consumidores_app')->withErrors(array('success' => 'Alterado seu painel!'));

    }



    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function promocoes(Request $request, $cidade)
    {

        if($request->session()->has('logadoHome')) {
            return redirect()->route($request->session()->get('logadoHome').'_app');
        }

        $promocoes = Promocoes::select('promocoes.*','clientes.nome', 'categorias.nome AS categoria')
                            ->join('categorias','categorias.id','=','promocoes.categorias_id')
                            ->join('clientes','clientes.id','=','promocoes.clientes_id')
                            ->where('clientes.cidade','=',$cidade)
                            ->whereRaw('dtinicio <= CURDATE() AND dtfim >= CURDATE()')
                            ->orderBy(DB::raw('RAND()'))
                            ->get();

        $params = array(
            'cidade' => $cidade,
            'promocoes' => $promocoes,
            'tituloPagina' => 'Promocões - '.$cidade,
            'template' => 'promocoes'
        );

        return view('home')->with($params);
    }



    public function avaliacao(Request $request)
    {

        $usuario = $request->session()->get('logadoHome_usuario');

        $dados = array(
            'consumidores_id' => $usuario->id,
            'clientes_id' => $usuario->cliente->id,
            'nota' => $request->get('nota'),
            'comentario' => $request->get('comentario'),
        );

        $salvo = Nps::salvar($dados);


        return redirect()->route('consumidores_app')->withErrors(array('success' => 'Muito obrigado pela avaliação!'));

    }

    public function calculadora()
    {
        return view('calculadora');
    }

}

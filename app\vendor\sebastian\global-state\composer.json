{"name": "sebastian/global-state", "description": "Snapshotting of global state", "keywords": ["global state"], "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "prefer-stable": true, "config": {"optimize-autoloader": true, "sort-packages": true}, "require": {"php": ">=7.2", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^8.0"}, "suggest": {"ext-uopz": "*"}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/_fixture/"], "files": ["tests/_fixture/SnapshotFunctions.php"]}, "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}
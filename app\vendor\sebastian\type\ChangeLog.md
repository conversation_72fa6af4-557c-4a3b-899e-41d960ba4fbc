# ChangeLog

All notable changes are documented in this file using the [Keep a CHANGELOG](http://keepachangelog.com/) principles.

## [1.1.4] - 2020-11-30

### Changed

* Changed PHP version constraint in `composer.json` from `^7.2` to `>=7.2`

## [1.1.3] - 2019-07-02

### Fixed

* Fixed class name comparison in `ObjectType` to be case insensitive

## [1.1.2] - 2019-06-19

### Fixed

* Fixed handling of `object` type

## [1.1.1] - 2019-06-08

### Fixed

* Fixed autoloading of `callback_function.php` fixture file

## [1.1.0] - 2019-06-07

### Added

* Added support for `callable` type
* Added support for `iterable` type

## [1.0.0] - 2019-06-06

* Initial release based on [code contributed by <PERSON> to PHPUnit](https://github.com/sebastianbergmann/phpunit/pull/3673)

[1.1.4]: https://github.com/sebastian<PERSON>mann/type/compare/1.1.3...1.1.4
[1.1.3]: https://github.com/sebastianbergmann/type/compare/1.1.2...1.1.3
[1.1.2]: https://github.com/sebastianbergmann/type/compare/1.1.1...1.1.2
[1.1.1]: https://github.com/sebastianbergmann/type/compare/1.1.0...1.1.1
[1.1.0]: https://github.com/sebastianbergmann/type/compare/1.0.0...1.1.0
[1.0.0]: https://github.com/sebastianbergmann/type/compare/ff74aa41746bd8d10e931843ebf37d42da513ede...1.0.0

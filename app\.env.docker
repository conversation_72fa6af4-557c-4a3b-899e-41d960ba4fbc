APP_NAME=Wincash
APP_ENV=production
APP_KEY=base64:cpKlssE0GzCnMjcF9wgOX56a80erpZZ8T5snRWYjbgE=
APP_DEBUG=true
APP_URL=http://127.0.0.1:8000

LOG_CHANNEL=stack

DB_CONNECTION=mysql
#DB_HOST=127.0.0.1
#DB_HOST=db
#DB_PORT=3306
#DB_DATABASE=wincashbrcom_dev_app
#DB_USERNAME=wincashbrcom_dev
#DB_PASSWORD="Acesso@2017@#"
#DB_PASSWORD=root
DB_HOST=************
DB_PORT=3306
DB_DATABASE=wincash_dev
DB_USERNAME=mysql
DB_PASSWORD=8a94818823ba51ca9936

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=wincashbr.com.br
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD="@Yq_EEoA6EUw"
MAIL_ENCRYPTION=ssl
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

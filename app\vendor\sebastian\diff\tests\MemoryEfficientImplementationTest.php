<?php declare(strict_types=1);
/*
 * This file is part of sebastian/diff.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON>\Diff;

/**
 * @covers <PERSON><PERSON><PERSON><PERSON>n\Diff\MemoryEfficientLongestCommonSubsequenceCalculator
 */
final class MemoryEfficientImplementationTest extends LongestCommonSubsequenceTest
{
    protected function createImplementation(): LongestCommonSubsequenceCalculator
    {
        return new MemoryEfficientLongestCommonSubsequenceCalculator;
    }
}

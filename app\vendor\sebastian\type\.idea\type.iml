<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/tests" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/instantiator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpdocumentor/reflection-common" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpdocumentor/reflection-docblock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpdocumentor/type-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpspec/prophecy" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-token-stream" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/resource-operations" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/webmozart/assert" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>
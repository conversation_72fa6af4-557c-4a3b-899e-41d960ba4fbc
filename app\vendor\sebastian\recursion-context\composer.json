{"name": "sebastian/recursion-context", "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}}
# ChangeLog

All notable changes are documented in this file using the [Keep a CHANGELOG](http://keepachangelog.com/) principles.

## [2.1.3] - 2020-11-30

### Changed

* Changed PHP version constraint in `composer.json` from `^7.1` to `>=7.1`

## [2.1.2] - 2019-06-07

### Fixed

* Fixed [#21](https://github.com/sebastianbergmann/php-timer/pull/3352): Formatting of memory consumption does not work on 32bit systems

## [2.1.1] - 2019-02-20

### Changed

* Improved formatting of memory consumption for `resourceUsage()`

## [2.1.0] - 2019-02-20

### Changed

* Improved formatting of memory consumption for `resourceUsage()`

## [2.0.0] - 2018-02-01

### Changed

* This component now uses namespaces

### Removed

* This component is no longer supported on PHP 5.3, PHP 5.4, PHP 5.5, PHP 5.6, and PHP 7.0

[2.1.3]: https://github.com/sebastian<PERSON>mann/diff/compare/2.1.2...2.1.3
[2.1.2]: https://github.com/sebastianbergmann/diff/compare/2.1.1...2.1.2
[2.1.1]: https://github.com/sebastianbergmann/diff/compare/2.1.0...2.1.1
[2.1.0]: https://github.com/sebastianbergmann/diff/compare/2.0.0...2.1.0
[2.0.0]: https://github.com/sebastianbergmann/diff/compare/1.0.9...2.0.0

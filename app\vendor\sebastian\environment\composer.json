{"name": "sebastian/environment", "description": "Provides functionality to handle HHVM/PHP environments", "keywords": ["environment", "hhvm", "xdebug"], "homepage": "http://www.github.com/sebastianbergmann/environment", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "config": {"optimize-autoloader": true, "sort-packages": true}, "prefer-stable": true, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "4.2-dev"}}}
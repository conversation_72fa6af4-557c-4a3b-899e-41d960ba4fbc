<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCategoriasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('categorias', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('nome');
            $table->timestamps();
        });

        // Inserir dados iniciais
        DB::table('categorias')->insert([
            ['nome' => 'Alimentação', 'created_at' => now(), 'updated_at' => now()],
            ['nome' => 'Combustível', 'created_at' => now(), 'updated_at' => now()],
            ['nome' => 'Farmácia', 'created_at' => now(), 'updated_at' => now()],
            ['nome' => 'Supermercado', 'created_at' => now(), 'updated_at' => now()],
            ['nome' => 'Varejo', 'created_at' => now(), 'updated_at' => now()],
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('categorias');
    }
}
